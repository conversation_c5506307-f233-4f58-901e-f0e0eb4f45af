---
apiVersion: saas.dynatrace.observability.careem.com/v1
kind: AnomalyDetection
metadata:
  name: aws-rds-anomaly
  labels:
    scope: production
spec:
  anomalies:
    staticThreshold:
      CPUUtilization:
        title: "[global] RDS High CPU Usage"
        description: "CPU utilization alarm for utilization greater than 80%"
        threshold: 80
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.cpuUtilizationByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "High CPU Usage"
      FreeStorageSpace:
        title: "[global] RDS Low Free Storage Space"
        description: "Lower disk alarm for 5% of allocated storage space"
        threshold: **********
        alertCondition: "BELOW"
        query: |
          timeseries avg(cloud.aws.rds.freeStorageSpaceByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "Low Free Storage Space"
      FreeableMemory:
        title: "[global] RDS Low Freeable Memory"
        description: "Freeable memory alarm for lower memory (1GB threshold)"
        threshold: **********
        alertCondition: "BELOW"
        query: |
          timeseries avg(cloud.aws.rds.freeableMemoryByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "Low Freeable Memory"
      ReplicaLag:
        title: "[global] RDS High Replica Lag"
        description: "Replica lag alarm if maximum lag over 2 minutes"
        threshold: 120
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.replicaLagByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Replica Lag"
      DeadLocks:
        title: "[global] RDS Deadlocks Detected"
        description: "Alert when deadlocks are detected in the database"
        threshold: 1
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.deadlocksByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "ERROR_EVENT"
        eventName: "Deadlocks Detected"
      DiskQueueDepth:
        title: "[global] RDS High Disk Queue Depth"
        description: "Queue depth alarm if count is over 11 for extended period"
        threshold: 11
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.diskQueueDepthByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Disk Queue Depth"
      ReadLatency:
        title: "[global] RDS High Read Latency"
        description: "Average read latency is greater than 100 milliseconds"
        threshold: 0.1
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.readLatencyByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Read Latency"
      WriteLatency:
        title: "[global] RDS High Write Latency"
        description: "Average write latency is greater than 100 milliseconds"
        threshold: 0.1
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.writeLatencyByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Write Latency"
    adaptiveThreshold:
      DatabaseConnections:
        title: "[global] RDS High Database Connections"
        description: "Alert on RDS High Database Connections"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.databaseConnectionsByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "RESOURCE_CONTENTION_EVENT"
        eventName: "High Database Connections"
      DiskQueueDepth:
        title: "[global] RDS High Disk Queue Depth"
        description: "Alert on RDS High Disk Queue Depth"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.diskQueueDepthByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Disk Queue Depth"
      ReadLatency:
        title: "[global] RDS High Read Latency"
        description: "Alert on RDS High Read Latency"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.readLatencyByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Read Latency"
      WriteLatency:
        title: "[global] RDS High Write Latency"
        description: "Alert on RDS High Write Latency"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.writeLatencyByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Write Latency"
      ReadIOPS:
        title: "[global] RDS High Read IOPS"
        description: "Alert on RDS High Read IOPS"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.readIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Read IOPS"
      WriteIOPS:
        title: "[global] RDS High Write IOPS"
        description: "Alert on RDS High Write IOPS"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.writeIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Write IOPS"
      RowLockTime:
        title: "[global] RDS Row Lock Time"
        description: "Alert on RDS Row Lock Time"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.rowLockTimeByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Row Lock Time"
      MaximumUsedTransactionIDs:
        title: "[global] RDS Maximum Used Transaction IDs"
        description: "Alert on RDS Maximum Used Transaction IDs"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.maximumUsedTransactionIDsByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High Maximum Used Transaction IDs"
      LVMReadIOPS:
        title: "[global] RDS LVM Read IOPS"
        description: "Alert on RDS LVM Read IOPS"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.lvmReadIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High LVM Read IOPS"
      LVMWriteIOPS:
        title: "[global] RDS LVM Write IOPS"
        description: "Alert on RDS LVM Write IOPS"
        alertCondition: "ABOVE"
        query: |
          timeseries avg(cloud.aws.rds.lvmWriteIOPSByAccountIdDBInstanceIdentifierRegion), by:{`dt.entity.cloud:aws:rds:db`,dbinstanceidentifier}
        entityIdProperty: "dt.entity.cloud:aws:rds:db"
        entityNameProperty: "dbinstanceidentifier"
        eventType: "PERFORMANCE_EVENT"
        eventName: "High LVM Write IOPS"
