#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fetch all Dynatrace metrics using the provided curl command
and save them to a JSON file.
"""

import requests
import json
import time
from datetime import datetime

def fetch_dynatrace_metrics():
    """
    Fetch all Dynatrace metrics using the API endpoint from the curl command
    """
    
    # Base URL and headers from the curl command
    base_url = "https://hib45559.live.dynatrace.com/rest/v2/metrics"
    
    headers = {
        'Accept': 'application/json; charset=utf-8',
        'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json; charset=utf-8',
        'Referer': 'https://hib45559.live.dynatrace.com/ui/metrics?gtf=-2h&gf=all',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Tab-Id': 'tab-71h6kGs3cigK',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'X-CSRFToken': '2c3a069f-725d-4202-b2d3-4b78fb0b1273|7|prod34-ireland',
        'X-Last-Action': '1748857502631',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'x-dtpc': '6$457419785_471h56vBIFTCRNDJTRFSMQEMFCGEVWGFNMNFDOB-0e0'
    }
    
    cookies = {
        'rxVisitor': '1724926346372VFVFQAB4L2CDT9V8SIRLK1ADBOQ1EV6J',
        'OptanonConsent': 'isGpcEnabled=0&datestamp=Thu+Apr+17+2025+15%3A41%3A08+GMT%2B0300+(GMT%2B03%3A00)&version=202409.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=04ad88f5-c915-43d8-997c-1d9d59bf3fdf&interactionCount=0&isAnonUser=1&landingPath=https%3A%2F%2Fdocs.dynatrace.com%2Fdocs%2Fingest-from%2Famazon-web-services%2Fintegrate-with-aws%2Faws-all-services%2Faws-service-relational-database-service-rds-builtin&groups=C0001%3A1%2CC0003%3A0%2CC0002%3A0%2CC0004%3A0',
        'ssoCSRFCookie': '386f843df3fa86f7c0831045442732a166a5aac890d83c05b1bcdeb8d983f7d1',
        'rxVisitorkle28kkx': '1748856953015M7N12LAVFVTEKGOU4DF3KUK0VUOEGQFT',
        'dtSakle28kkx': '-',
        'dtSa': '-',
        'apmsessionid': 'node0115t43yd8ydys1q6pciptoemwv3393.node0',
        'SRV': 'server7',
        'dtCookie': 'v_4_srv_12_sn_DDFC079FE1C3709AFCF30E093137039B_perc_100000_ol_0_mul_1_app-3Aa0a6928b5db408e9_1_app-3A98ef57ca1ba5392b_1_app-3Ada88f4b65c5e92a4_1_rcs-3Acss_0',
        'rxvt': '1748859274505|1748856955506',
        'dtPC': '12$457417998_917h-vFAJJDMKAFMLJIMIWPWPQFDFCVFHPUIKF-0e0',
        'dtCookiekle28kkx': 'v_4_srv_6_sn_7C75C8C56BA1C1F46250F88E1EDE18AB_perc_100000_ol_0_mul_1_app-3A4b7060b65fa3d688_1_app-3Ad2f4a377001040b0_1_rcs-3Acss_0',
        'rxvtkle28kkx': '1748859304226|1748856953017',
        'dtPCkle28kkx': '6$457419785_471h56vBIFTCRNDJTRFSMQEMFCGEVWGFNMNFDOB-0e0'
    }
    
    all_metrics = []
    next_page_key = None
    page_count = 0
    
    # Calculate time range (last 2 hours as in original curl)
    current_time = int(time.time() * 1000)
    from_time = current_time - (2 * 60 * 60 * 1000)  # 2 hours ago
    
    print("Starting to fetch Dynatrace metrics...")
    print(f"Time range: {from_time} to {current_time}")
    
    while True:
        page_count += 1
        print(f"Fetching page {page_count}...")
        
        # Parameters for the request
        params = {
            'pageSize': '500',
            'fields': 'displayName,metricId,description,unit,aggregationTypes,defaultAggregation,dimensionDefinitions,entityType,tags,warnings',
            'from': str(from_time),
            'to': str(current_time)
        }
        
        # Add next page key if available
        if next_page_key:
            params['nextPageKey'] = next_page_key
        
        try:
            response = requests.get(base_url, headers=headers, cookies=cookies, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if 'metrics' in data:
                metrics_batch = data['metrics']
                all_metrics.extend(metrics_batch)
                print(f"  Retrieved {len(metrics_batch)} metrics (total: {len(all_metrics)})")
            
            # Check if there are more pages
            if 'nextPageKey' in data and data['nextPageKey']:
                next_page_key = data['nextPageKey']
                time.sleep(0.5)  # Small delay to avoid rate limiting
            else:
                break
                
        except requests.exceptions.RequestException as e:
            print(f"Error fetching page {page_count}: {e}")
            break
    
    return all_metrics

def save_metrics_to_file(metrics, filename):
    """
    Save metrics to a JSON file with pretty formatting
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    full_filename = f"{filename}_{timestamp}.json"
    
    try:
        with open(full_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'total_metrics': len(metrics),
                'metrics': metrics
            }, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully saved {len(metrics)} metrics to {full_filename}")
        return full_filename
    except Exception as e:
        print(f"Error saving metrics to file: {e}")
        return None

def main():
    """
    Main function to fetch and save Dynatrace metrics
    """
    print("Dynatrace Metrics Fetcher")
    print("=" * 50)
    
    # Fetch all metrics
    metrics = fetch_dynatrace_metrics()
    
    if metrics:
        print(f"\nTotal metrics fetched: {len(metrics)}")
        
        # Save to file
        filename = save_metrics_to_file(metrics, "dynatrace_metrics")
        
        if filename:
            print(f"\nMetrics saved to: {filename}")
            
            # Print some sample metrics for verification
            print("\nSample metrics (first 3):")
            for i, metric in enumerate(metrics[:3]):
                print(f"{i+1}. {metric.get('metricId', 'N/A')} - {metric.get('displayName', 'N/A')}")
        
    else:
        print("No metrics were fetched.")

if __name__ == "__main__":
    main()
