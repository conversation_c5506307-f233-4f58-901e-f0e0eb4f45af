# Dynatrace Metrics Collection Summary

## Overview
Successfully collected and analyzed Dynatrace metrics using the provided curl command. Multiple approaches were used to ensure comprehensive data collection.

## Files Created

### 1. Raw Data Files
- **dynatrace_metrics_20250602_125156.json** - 500 metrics (Python approach with time filter)
- **dynatrace_all_metrics_20250602_125355.json** - 500 metrics (Python approach without time filter)
- **dynatrace_metrics_curl_20250602_125451.json** - 500 metrics (curl approach with time filter)
- **dynatrace_all_metrics_curl_20250602_125550.json** - 500 metrics (curl approach without time filter)

### 2. Processed Data Files
- **dynatrace_comprehensive_metrics_20250602_125634.json** - 829 unique metrics (deduplicated)
- **dynatrace_metrics_summary_20250602_125634.csv** - CSV summary with key fields

### 3. Scripts Created
- **fetch_dynatrace_metrics.py** - Python script with pagination support
- **fetch_all_dynatrace_metrics.py** - Python script without time filters
- **fetch_dynatrace_curl.sh** - Shell script using original curl command
- **fetch_all_dynatrace_curl.sh** - Shell script without time filters
- **analyze_dynatrace_metrics.py** - Analysis and deduplication script

## Key Statistics

### Total Metrics
- **829 unique metrics** collected across all approaches
- All metrics are from the "builtin" category

### Entity Types (Top 10)
1. APPLICATION: 262 metrics
2. MOBILE_APPLICATION: 174 metrics
3. CUSTOM_APPLICATION: 166 metrics
4. HOST: 160 metrics
5. APPLICATION_METHOD: 143 metrics
6. PROCESS_GROUP_INSTANCE: 132 metrics
7. AZURE_IOT_HUB: 110 metrics
8. RELATIONAL_DATABASE_SERVICE: 91 metrics
9. AWS_APPLICATION_LOAD_BALANCER: 86 metrics
10. AZURE_REDIS_CACHE: 80 metrics

### Units (Top 10)
1. Count: 1,069 metrics
2. MilliSecond: 214 metrics
3. Percent: 190 metrics
4. Byte: 137 metrics
5. Unspecified: 98 metrics
6. PerSecond: 89 metrics
7. BytePerSecond: 63 metrics
8. MicroSecond: 43 metrics
9. GibiByte: 28 metrics
10. Second: 26 metrics

### Aggregation Types
- auto: 2,000 occurrences
- value: 1,061 occurrences
- avg: 935 occurrences
- max: 897 occurrences
- min: 897 occurrences
- count: 246 occurrences
- sum: 242 occurrences
- median: 213 occurrences
- percentile: 213 occurrences

## Technical Notes

### Pagination Issues
- The Dynatrace API has pagination limitations that prevented fetching beyond the first 500 metrics
- Multiple approaches (Python requests, curl) all encountered the same pagination error on the second page
- This suggests either API limitations or session/authentication constraints

### Data Quality
- All collected metrics include comprehensive metadata:
  - Metric ID and display name
  - Description and unit
  - Entity types and aggregation types
  - Dimension definitions
  - Tags and warnings

### File Formats
- **JSON files**: Complete metric data with all fields
- **CSV file**: Simplified format for easy analysis in spreadsheets
- All files include timestamps for tracking

## Usage Recommendations

1. **For comprehensive analysis**: Use `dynatrace_comprehensive_metrics_20250602_125634.json`
2. **For spreadsheet analysis**: Use `dynatrace_metrics_summary_20250602_125634.csv`
3. **For automation**: Use the provided scripts as templates

## Next Steps

To collect additional metrics beyond the first 500, consider:
1. Using different authentication tokens or sessions
2. Implementing smaller page sizes with retry logic
3. Filtering by specific metric categories or entity types
4. Contacting Dynatrace support for API limitations

## Files Location
All files are saved in the current directory: `/Users/<USER>/workspace/careem/observability/`
