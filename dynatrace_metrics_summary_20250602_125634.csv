MetricID,DisplayName,Description,Unit,EntityTypes,AggregationTypes,DimensionCount
"builtin:apps.custom.reportedErrorCount","Reported error count (by OS; app version) [custom]","The number of all reported errors.","Count","","auto|value",0
"builtin:apps.custom.sessionCount","Session count (by OS; app version) [custom]","The number of captured user sessions.","Count","","auto|value",0
"builtin:apps.mobile.sessionCount","Session count (by OS; app version; crash replay feature status) [mobile]","The number of captured user sessions.","Count","MOBILE_APPLICATION","auto|value",4
"builtin:apps.mobile.sessionCount.sessionReplayStatus","Session count (by OS; app version; full replay feature status) [mobile]","The number of captured user sessions.","Count","","auto|value",0
"builtin:apps.mobile.reportedErrorCount","Reported error count (by OS; app version) [mobile]","The number of all reported errors.","Count","","auto|value",0
"builtin:apps.other.apdex.osAndGeo","Apdex (by OS; geolocation) [mobile; custom]","The Apdex rating for all captured user actions.","Unspecified","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.apdex.osAndVersion","Apdex (by OS; app version) [mobile; custom]","The Apdex rating for all captured user actions.","Unspecified","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.crashAffectedUsers.os","User count - estimated users affected by crashes (by OS) [mobile; custom]","The estimated number of unique users affected by a crash. For this high cardinality metric; the HyperLogLog algorithm is used to approximate the number of users.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",2
"builtin:apps.other.crashAffectedUsers.osAndVersion-std","User count - estimated users affected by crashes (by OS; app version) [mobile; custom]","The estimated number of unique users affected by a crash. For this high cardinality metric; the HyperLogLog algorithm is used to approximate the number of users.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.crashAffectedUsersRate.os","User rate - estimated users affected by crashes (by OS) [mobile; custom]","The estimated percentage of unique users affected by a crash. For this high cardinality metric; the HyperLogLog algorithm is used to approximate the number of users.","Percent","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",2
"builtin:apps.other.crashCount.osAndGeo","Crash count (by OS; geolocation) [mobile; custom]","The number of detected crashes.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.crashCount.osAndVersion","Crash count (by OS; app version) [mobile; custom]","The number of detected crashes.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.crashCount.osAndVersion-std","Crash count (by OS; app version) [mobile; custom]","The number of detected crashes.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.crashFreeUsersRate.os","User rate - estimated crash free users (by OS) [mobile; custom]","The estimated percentage of unique users not affected by a crash. For this high cardinality metric; the HyperLogLog algorithm is used to approximate the number of users.","Percent","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",2
"builtin:apps.other.keyUserActions.apdexValue.os","Apdex (by key user action; OS) [mobile; custom]","The Apdex rating for all captured key user actions.","Unspecified","DEVICE_APPLICATION_METHOD","auto|value",2
"builtin:apps.other.keyUserActions.count.osAndApdex","Action count (by key user action; OS; Apdex category) [mobile; custom]","The number of captured key user actions.","Count","DEVICE_APPLICATION_METHOD","auto|value",3
"builtin:apps.other.keyUserActions.duration.os","Action duration (by key user action; OS) [mobile; custom]","The duration of key user actions.","MicroSecond","DEVICE_APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.other.keyUserActions.reportedErrorCount.os","Reported error count (by key user action; OS) [mobile; custom]","The number of reported errors for key user actions.","Count","DEVICE_APPLICATION_METHOD","auto|value",2
"builtin:apps.other.keyUserActions.requestCount.os","Request count (by key user action; OS) [mobile; custom]","The number of captured web requests associated with key user actions.","Count","DEVICE_APPLICATION_METHOD","auto|value",2
"builtin:apps.other.keyUserActions.requestDuration.os","Request duration (by key user action; OS) [mobile; custom]","The duration of web requests for key user actions. Be aware that this metric is measured in microseconds while other request duration metrics for mobile and custom apps are measured in milliseconds.","MilliSecond","DEVICE_APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.other.keyUserActions.requestErrorCount.os","Request error count (by key user action; OS) [mobile; custom]","The number of detected web request errors for key user actions.","Count","DEVICE_APPLICATION_METHOD","auto|value",2
"builtin:apps.other.keyUserActions.requestErrorRate.os","Request error rate (by key user action; OS) [mobile; custom]","The percentage of web requests with detected errors for key user actions","Percent","DEVICE_APPLICATION_METHOD","auto|value",2
"builtin:apps.other.newUsers.os","New user count (by OS) [mobile; custom]","The number of users that launched the application(s) for the first time. The metric is tied to specific devices; so users are counted multiple times if they install the application on multiple devices. The metric doesn't distinguish between multiple users that share the same device and application installation.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",2
"builtin:apps.other.requestCount.osAndProvider","Request count (by OS; provider) [mobile; custom]","The number of captured web requests.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",4
"builtin:apps.other.requestCount.osAndVersion","Request count (by OS; app version) [mobile; custom]","The number of captured web requests.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.requestErrorCount.osAndProvider","Request error count (by OS; provider) [mobile; custom]","The number of detected web request errors.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",4
"builtin:apps.other.requestErrorCount.osAndVersion","Request error count (by OS; app version) [mobile; custom]","The number of detected web request errors.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.requestErrorRate.osAndProvider","Request error rate (by OS; provider) [mobile; custom]","The percentage of web requests with detected errors.","Percent","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",4
"builtin:apps.other.requestErrorRate.osAndVersion","Request error rate (by OS; app version) [mobile; custom]","The percentage of web requests with detected errors.","Percent","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.requestTimes.osAndProvider","Request duration (by OS; provider) [mobile; custom]","The duration of web requests.","MilliSecond","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|avg|count|max|median|min|percentile|sum",4
"builtin:apps.other.requestTimes.osAndVersion","Request duration (by OS; app version) [mobile; custom]","The duration of web requests.","MilliSecond","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|avg|count|max|median|min|percentile|sum",3
"builtin:apps.other.sessionCount.agentVersionAndOs","Session count (by agent version; OS) [mobile; custom]","The number of captured user sessions.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.sessionCount.osAndCrashReportingLevel","Session count (by OS; crash reporting level) [mobile; custom]","The number of captured user sessions.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.sessionCount.osAndDataCollectionLevel","Session count (by OS; data collection level) [mobile; custom]","The number of captured user sessions.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.sessionCount.osAndGeo","Session count - estimated (by OS; geolocation) [mobile; custom]","The estimated number of captured user sessions. For this high cardinality metric; the HyperLogLog algorithm is used to approximate the number of sessions.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.sessionCount.osAndVersion-std","Session count (by OS; app version) [mobile; custom]","The number of captured user sessions.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.uaCount.geoAndApdex","Action count (by geolocation; Apdex category) [mobile; custom]","The number of captured user actions.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.uaCount.osAndApdex","Action count (by OS; Apdex category) [mobile; custom]","The number of captured user actions.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.uaCount.osAndVersion","Action count (by OS; app version) [mobile; custom]","The number of captured user actions.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.uaDuration.osAndVersion","Action duration (by OS; app version) [mobile; custom]","The duration of user actions.","MicroSecond","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|avg|count|max|median|min|percentile|sum",3
"builtin:apps.other.userCount.osAndGeo","User count - estimated (by OS; geolocation) [mobile; custom]","The estimated number of unique users that have a mapped geolocation. The metric is based on 'internalUserId'. When 'dataCollectionLevel' is set to 'performance' or 'off'; 'internalUserId' is changed at each app start. For this high cardinality metric; the HyperLogLog algorithm is used to approximate the number of users.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.other.userCount.osAndVersion-std","User count - estimated (by OS; app version) [mobile; custom]","The estimated number of unique users. The metric is based on 'internalUserId'. When 'dataCollectionLevel' is set to 'performance' or 'off'; 'internalUserId' is changed at each app start. For this high cardinality metric; the HyperLogLog algorithm is used to approximate the number of users.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",3
"builtin:apps.web.action.affectedUas","User action rate - affected by JavaScript errors (by key user action; user type) [web]","The percentage of key user actions with detected JavaScript errors.","Percent","APPLICATION_METHOD","auto|value",2
"builtin:apps.web.action.apdex","Apdex (by key user action) [web]","The average Apdex rating for key user actions.","Unspecified","APPLICATION_METHOD","auto|avg",2
"builtin:apps.web.action.count.custom.browser","Action count - custom action (by key user action; browser) [web]","The number of custom actions that are marked as key user actions.","Count","APPLICATION_METHOD","auto|value",2
"builtin:apps.web.action.count.load.browser","Action count - load action (by key user action; browser) [web]","The number of load actions that are marked as key user actions.","Count","APPLICATION_METHOD","auto|value",2
"builtin:apps.web.action.count.xhr.browser","Action count - XHR action (by key user action; browser) [web]","The number of XHR actions that are marked as key user actions.","Count","APPLICATION_METHOD","auto|value",2
"builtin:apps.web.action.cumulativeLayoutShift.load.userType","Cumulative Layout Shift - load action (by key user action; user type) [web]","The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions that are marked as key user actions.","Unspecified","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.cumulativeLayoutShift.load.userType.geo","Cumulative Layout Shift - load action (by key user action; geolocation; user type) [web]","The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions that are marked as key user actions.","Unspecified","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",3
"builtin:apps.web.action.cumulativeLayoutShift.load.browser","Cumulative Layout Shift - load action (by key user action; browser) [web]","The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions that are marked as key user actions.","Unspecified","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.domInteractive.load.browser","DOM interactive - load action (by key user action; browser) [web]","The time taken until a page's status is set to "interactive" and it's ready to receive user input. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.duration.custom.browser","Action duration - custom action (by key user action; browser) [web]","The duration of custom actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.duration.load.browser","Action duration - load action (by key user action; browser) [web]","The duration of load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.duration.xhr.browser","Action duration - XHR action (by key user action; browser) [web]","The duration of XHR actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.firstByte.load.browser","Time to first byte - load action (by key user action; browser) [web]","The time taken until the first byte of the response is received from the server; relevant application caches; or a local resource. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.firstByte.xhr.browser","Time to first byte - XHR action (by key user action; browser) [web]","The time taken until the first byte of the response is received from the server; relevant application caches; or a local resource. Calculated for XHR actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.firstInputDelay.load.userType","First Input Delay - load action (by key user action; user type) [web]","The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.firstInputDelay.load.userType.geo","First Input Delay - load action (by key user action; geolocation; user type) [web]","The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",3
"builtin:apps.web.action.firstInputDelay.load.browser","First Input Delay - load action (by key user action; browser) [web]","The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.largestContentfulPaint.load.userType","Largest Contentful Paint - load action (by key user action; user type) [web]","The time taken to render the largest element in the viewport. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.largestContentfulPaint.load.userType.geo","Largest Contentful Paint - load action (by key user action; geolocation; user type) [web]","The time taken to render the largest element in the viewport. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",3
"builtin:apps.web.action.largestContentfulPaint.load.browser","Largest Contentful Paint - load action (by key user action; browser) [web]","The time taken to render the largest element in the viewport. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.loadEventEnd.load.browser","Load event end - load action (by key user action; browser) [web]","The time taken to complete the load event of a page. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.loadEventStart.load.browser","Load event start - load action (by key user action; browser) [web]","The time taken to begin the load event of a page. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.networkContribution.load","Network contribution - load action (by key user action; user type) [web]","The time taken to request and receive resources (including DNS lookup; redirect; and TCP connect time). Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.networkContribution.xhr","Network contribution - XHR action (by key user action; user type) [web]","The time taken to request and receive resources (including DNS lookup; redirect; and TCP connect time). Calculated for XHR actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.responseEnd.load.browser","Response end - load action (by key user action; browser) [web]","(AKA HTML downloaded) The time taken until the user agent receives the last byte of the response or the transport connection is closed; whichever comes first. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.responseEnd.xhr.browser","Response end - XHR action (by key user action; browser) [web]","The time taken until the user agent receives the last byte of the response or the transport connection is closed; whichever comes first. Calculated for XHR actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.serverContribution.load","Server contribution - load action (by key user action; user type) [web]","The time spent on server-side processing for a page. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.serverContribution.xhr","Server contribution - XHR action (by key user action; user type) [web]","The time spent on server-side processing for a page. Calculated for XHR actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.speedIndex.load.browser","Speed index - load action (by key user action; browser) [web]","The score measuring how quickly the visible parts of a page are rendered. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.visuallyComplete.load.browser","Visually complete - load action (by key user action; browser) [web]","The time taken to fully render content in the viewport. Calculated for load actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.visuallyComplete.xhr.browser","Visually complete - XHR action (by key user action; browser) [web]","The time taken to fully render content in the viewport. Calculated for XHR actions that are marked as key user actions.","MilliSecond","APPLICATION_METHOD","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.action.countOfErrors","Error count (by key user action; user type; error type; error origin) [web]","The number of detected errors that occurred during key user actions.","Count","APPLICATION_METHOD","auto|value",4
"builtin:apps.web.action.countOfUserActionsWithErrors","User action count with errors (by key user action; user type) [web]","The number of key user actions with detected errors.","Count","APPLICATION_METHOD","auto|value",2
"builtin:apps.web.action.jsErrorsDuringUa","JavaScript errors count during user actions (by key user action; user type) [web]","The number of detected JavaScript errors that occurred during key user actions.","Count","APPLICATION_METHOD","auto|value",2
"builtin:apps.web.action.jsErrorsWithoutUa","JavaScript error count without user actions (by key user action; user type) [web]","The number of detected standalone JavaScript errors (occurred between key user actions).","Count","APPLICATION_METHOD","auto|value",2
"builtin:apps.web.action.percentageOfUserActionsAffectedByErrors","User action rate - affected by errors (by key user action; user type) [web]","The percentage of key user actions with detected errors.","Percent","APPLICATION_METHOD","auto|value",2
"builtin:apps.web.actionCount.custom.browser","Action count - custom action (by browser) [web]","The number of custom actions.","Count","APPLICATION","auto|value",2
"builtin:apps.web.actionCount.load.browser","Action count - load action (by browser) [web]","The number of load actions.","Count","APPLICATION","auto|value",2
"builtin:apps.web.actionCount.xhr.browser","Action count - XHR action (by browser) [web]","The number of XHR actions.","Count","APPLICATION","auto|value",2
"builtin:apps.web.actionCount.category","Action count (by Apdex category) [web]","The number of user actions.","Count","APPLICATION","auto|value",2
"builtin:apps.web.actionCount.summary","Action with key performance metric count (by action type; geolocation; user type) [web]","The number of user actions that have a key performance metric and mapped geolocation.","Count","APPLICATION","auto|value",4
"builtin:apps.web.actionDuration.custom.browser","Action duration - custom action (by browser) [web]","The duration of custom actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.actionDuration.load.browser","Action duration - load action (by browser) [web]","The duration of load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.actionDuration.xhr.browser","Action duration - XHR action (by browser) [web]","The duration of XHR actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.actionsPerSession","Actions per session average (by users; user type) [web]","The average number of user actions per user session.","Count","APPLICATION","auto|avg",3
"builtin:apps.web.activeSessions","Session count - estimated active sessions (by users; user type) [web]","The estimated number of active user sessions. An active session is one in which a user has been confirmed to still be active at a given time. For this high-cardinality metric; the HyperLogLog algorithm is used to approximate the session count.","Count","APPLICATION","auto|value",3
"builtin:apps.web.activeUsersEst","User count - estimated active users (by users; user type) [web]","The estimated number of unique active users. For this high-cardinality metric; the HyperLogLog algorithm is used to approximate the user count.","Count","APPLICATION","auto|value",3
"builtin:apps.web.affectedUas","User action rate - affected by JavaScript errors (by user type) [web]","The percentage of user actions with detected JavaScript errors.","Percent","APPLICATION","auto|value",2
"builtin:apps.web.apdex.userType","Apdex (by user type) [web]","","Unspecified","APPLICATION","auto|avg",2
"builtin:apps.web.apdex.userType.geoBig","Apdex (by geolocation; user type) [web]","The average Apdex rating for user actions that have a mapped geolocation.","Unspecified","APPLICATION","auto|avg",3
"builtin:apps.web.bouncedSessionRatio","Bounce rate (by users; user type) [web]","The percentage of sessions in which users viewed only a single page and triggered only a single web request. Calculated by dividing single-page sessions by all sessions.","Percent","APPLICATION","auto|value",3
"builtin:apps.web.conversionRate","Conversion rate - sessions (by users; user type) [web]","The percentage of sessions in which at least one conversion goal was reached. Calculated by dividing converted sessions by all sessions.","Percent","APPLICATION","auto|value",3
"builtin:apps.web.converted","Session count - converted sessions (by users; user type) [web]","The number of sessions in which at least one conversion goal was reached.","Count","APPLICATION","auto|value",3
"builtin:apps.web.cumulativeLayoutShift.load.userType","Cumulative Layout Shift - load action (by user type) [web]","The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.","Unspecified","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.cumulativeLayoutShift.load.userType.geo","Cumulative Layout Shift - load action (by geolocation; user type) [web]","The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.","Unspecified","APPLICATION","auto|avg|count|max|median|min|percentile|sum",3
"builtin:apps.web.cumulativeLayoutShift.load.browser","Cumulative Layout Shift - load action (by browser) [web]","The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.","Unspecified","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.domInteractive.load.browser","DOM interactive - load action (by browser) [web]","The time taken until a page's status is set to "interactive" and it's ready to receive user input. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.endedSessions","Session count - estimated ended sessions (by users; user type) [web]","The number of completed user sessions.","Count","APPLICATION","auto|value",3
"builtin:apps.web.event.count.rageClick","Rage click count [web]","The number of detected rage clicks.","Count","APPLICATION","auto|value",1
"builtin:apps.web.firstByte.load.browser","Time to first byte - load action (by browser) [web]","The time taken until the first byte of the response is received from the server; relevant application caches; or a local resource. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.firstByte.xhr.browser","Time to first byte - XHR action (by browser) [web]","The time taken until the first byte of the response is received from the server; relevant application caches; or a local resource. Calculated for XHR actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.firstInputDelay.load.userType","First Input Delay - load action (by user type) [web]","The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.firstInputDelay.load.userType.geo","First Input Delay - load action (by geolocation; user type) [web]","The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",3
"builtin:apps.web.firstInputDelay.load.browser","First Input Delay - load action (by browser) [web]","The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.largestContentfulPaint.load.userType","Largest Contentful Paint - load action (by user type) [web]","The time taken to render the largest element in the viewport. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.largestContentfulPaint.load.userType.geo","Largest Contentful Paint - load action (by geolocation; user type) [web]","The time taken to render the largest element in the viewport. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",3
"builtin:apps.web.largestContentfulPaint.load.browser","Largest Contentful Paint - load action (by browser) [web]","The time taken to render the largest element in the viewport. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.loadEventEnd.load.browser","Load event end - load action (by browser) [web]","The time taken to complete the load event of a page. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.loadEventStart.load.browser","Load event start - load action (by browser) [web]","The time taken to begin the load event of a page. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.networkContribution.load","Network contribution - load action (by user type) [web]","The time taken to request and receive resources (including DNS lookup; redirect; and TCP connect time). Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.networkContribution.xhr","Network contribution - XHR action (by user type) [web]","The time taken to request and receive resources (including DNS lookup; redirect; and TCP connect time). Calculated for XHR actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.responseEnd.load.browser","Response end - load action (by browser) [web]","(AKA HTML downloaded) The time taken until the user agent receives the last byte of the response or the transport connection is closed; whichever comes first. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.responseEnd.xhr.browser","Response end - XHR action (by browser) [web]","The time taken until the user agent receives the last byte of the response or the transport connection is closed; whichever comes first. Calculated for XHR actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.serverContribution.load","Server contribution - load action (by user type) [web]","The time spent on server-side processing for a page. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.serverContribution.xhr","Server contribution - XHR action (by user type) [web]","The time spent on server-side processing for a page. Calculated for XHR actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.sessionDuration","Session duration (by users; user type) [web]","The average duration of user sessions.","MicroSecond","APPLICATION","auto|avg",3
"builtin:apps.web.speedIndex.load.browser","Speed index - load action (by browser) [web]","The score measuring how quickly the visible parts of a page are rendered. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.startedSessions","Session count - estimated started sessions (by users; user type) [web]","The number of started user sessions.","Count","APPLICATION","auto|value",3
"builtin:apps.web.visuallyComplete.load.browser","Visually complete - load action (by browser) [web]","The time taken to fully render content in the viewport. Calculated for load actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.visuallyComplete.xhr.browser","Visually complete - XHR action (by browser) [web]","The time taken to fully render content in the viewport. Calculated for XHR actions.","MilliSecond","APPLICATION","auto|avg|count|max|median|min|percentile|sum",2
"builtin:apps.web.countOfErrors","Error count (by user type; error type; error origin) [web]","The number of detected errors.","Count","APPLICATION","auto|value",4
"builtin:apps.web.countOfErrorsDuringUserActions","Error count during user actions (by user type; error type; error origin) [web]","The number of detected errors that occurred during user actions.","Count","APPLICATION","auto|value",4
"builtin:apps.web.countOfStandaloneErrors","Standalone error count (by user type; error type; error origin) [web]","The number of detected standalone errors (occurred between user actions).","Count","APPLICATION","auto|value",4
"builtin:apps.web.countOfUserActionsWithErrors","User action count - with errors (by user type) [web]","The number of key user actions with detected errors.","Count","APPLICATION","auto|value",2
"builtin:apps.web.errorCountForDavis","Error count for Davis (by user type; error type; error origin; error context)) [web]","The number of errors that were included in Davis AI problem detection and analysis.","Count","APPLICATION","auto|value",5
"builtin:apps.web.interactionToNextPaint","Interaction to next paint","","MilliSecond","APPLICATION","auto|count|max|median|min|percentile",4
"builtin:apps.web.jsErrorsDuringUa","JavaScript error count - during user actions (by user type) [web]","The number of detected JavaScript errors that occurred during user actions.","Count","APPLICATION","auto|value",2
"builtin:apps.web.jsErrorsWithoutUa","JavaScript error count - without user actions (by user type) [web]","The number of detected standalone JavaScript errors (occurred between user actions).","Count","APPLICATION","auto|value",2
"builtin:apps.web.percentageOfUserActionsAffectedByErrors","User action rate - affected by errors (by user type) [web]","The percentage of user actions with detected errors.","Percent","APPLICATION","auto|value",2
"builtin:billing.apps.custom.sessionsWithoutReplayByApplication","Session count - billed and unbilled [custom]","The number of billed and unbilled user sessions.  To get only the number of billed sessions; set the "Type" filter to "Billed".","Count","CUSTOM_APPLICATION","auto|value",2
"builtin:billing.apps.custom.userActionPropertiesByDeviceApplication","Total user action and session properties","The number of billed user action and user session properties.","Count","CUSTOM_APPLICATION","auto|value",1
"builtin:billing.apps.mobile.sessionsWithReplayByApplication","Session count - billed and unbilled - with Session Replay [mobile]","The number of billed and unbilled user sessions that include Session Replay data. To get only the number of billed sessions; set the "Type" filter to "Billed".","Count","MOBILE_APPLICATION","auto|value",2
"builtin:billing.apps.mobile.sessionsWithoutReplayByApplication","Session count - billed and unbilled [mobile]","The total number of billed and unbilled user sessions (with and without Session Replay data). To get only the number of billed sessions; set the "Type" filter to "Billed".","Count","MOBILE_APPLICATION","auto|value",2
"builtin:billing.apps.mobile.userActionPropertiesByMobileApplication","Total user action and session properties","The number of billed user action and user session properties.","Count","MOBILE_APPLICATION","auto|value",1
"builtin:billing.apps.web.sessionsWithReplayByApplication","Session count - billed and unbilled - with Session Replay [web]","The number of billed and unbilled user sessions that include Session Replay data. To get only the number of billed sessions; set the "Type" filter to "Billed".","Count","APPLICATION","auto|value",2
"builtin:billing.apps.web.sessionsWithoutReplayByApplication","Session count - billed and unbilled - without Session Replay [web]","The number of billed and unbilled user sessions that do not include Session Replay data. To get only the number of billed sessions; set the "Type" filter to "Billed".","Count","APPLICATION","auto|value",2
"builtin:billing.apps.web.userActionPropertiesByApplication","Total user action and session properties","The number of billed user action and user session properties.","Count","APPLICATION","auto|value",1
"builtin:billing.custom_events_classic.usage","(DPS) Total Custom Events Classic billing usage","The number of custom events ingested aggregated over all monitored entities. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.custom_events_classic.usage_by_entity","(DPS) Custom Events Classic billing usage by monitored entity","The number of custom events ingested split by monitored entity. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. For details on the events billed; refer to the usage_by_event_info metric. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.custom_events_classic.usage_by_event_info","(DPS) Custom Events Classic billing usage by event info","The number of custom events ingested split by event info. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. The info contains the context of the event plus the configuration ID. For details on the related monitored entities; refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","","auto|value",1
"builtin:billing.custom_metrics_classic.raw.usage_by_metric_key","(DPS) Recorded metric data points per metric key","The number of reported metric data points split by metric key. This metric does not account for included metric data points available to your environment.","Count","","auto|value",1
"builtin:billing.custom_metrics_classic.usage","(DPS) Total billed metric data points","The total number of metric data points after deducting the included metric data points. This is the rate-card value used for billing. Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.custom_metrics_classic.usage.foundation_and_discovery","(DPS) Total metric data points billable for Foundation & Discovery hosts","The number of metric data points billable for Foundation & Discovery hosts.","Count","","auto|value",0
"builtin:billing.custom_metrics_classic.usage.fullstack_hosts","(DPS) Total metric data points billed for Full-Stack hosts","The number of metric data points billed for Full-Stack hosts. To view the unadjusted usage per host; use builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host . This trailing metric is reported at 15-minute intervals with up to a 15-minute delay.","Count","","auto|value",0
"builtin:billing.custom_metrics_classic.usage.infrastructure_hosts","(DPS) Total metric data points billed for Infrastructure-monitored hosts","The number of metric data points billed for Infrastructure-monitored hosts. To view the unadjusted usage per host; use builtin:billing.infrastructure_monitoring.metric_data_points.ingested_by_host . This trailing metric is reported at 15-minute intervals with up to a 15-minute delay.","Count","","auto|value",0
"builtin:billing.custom_metrics_classic.usage.other","(DPS) Total metric data points billed by other entities","The number of metric data points billed that cannot be assigned to a host. The values reported in this metric are not eligible for included metric deduction and will be billed as is. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. o view the monitored entities that consume this usage; use the other_by_entity metric.","Count","","auto|value",0
"builtin:billing.custom_metrics_classic.usage.other_by_entity","(DPS) Billed metric data points reported and split by other entities","The number of billed metric data points split by entities that cannot be assigned to a host. The values reported in this metric are not eligible for included metric deduction and will be billed as is. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.custom_traces_classic.usage","(DPS) Total Custom Traces Classic billing usage","The number of spans ingested aggregated over all monitored entities. A span is a single operation within a distributed trace; ingested into Dynatrace. Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.custom_traces_classic.usage_by_entity","(DPS) Custom Traces Classic billing usage by monitored entity","The number of spans ingested split by monitored entity. A span is a single operation within a distributed trace; ingested into Dynatrace. For details on span types; refer to the usage_by_span_type metric. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.custom_traces_classic.usage_by_span_type","(DPS) Custom Traces Classic billing usage by span type","The number of spans ingested split by span type. A span is a single operation within a distributed trace; ingested into Dynatrace. Span kinds can be CLIENT; SERVER; PRODUCER; CONSUMER or INTERNAL For details on the related monitored entities; refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","","auto|value",1
"builtin:billing.ddu.events.byDescription","DDU events consumption by event info","License consumption of Davis data units by events pool split by event info","Unspecified","","auto|value",1
"builtin:billing.ddu.events.byEntity","DDU events consumption by monitored entity","License consumption of Davis data units by events pool split by monitored entity","Unspecified","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.ddu.events.total","Total DDU events consumption","Sum of license consumption of Davis data units aggregated over all monitored entities for the events pool","Unspecified","","auto|value",0
"builtin:billing.ddu.log.byDescription","DDU log consumption by log path","License consumption of Davis data units by log pool split by log path","Unspecified","","auto|value",1
"builtin:billing.ddu.log.byEntity","DDU log consumption by monitored entity","License consumption of Davis data units by log pool split by monitored entity","Unspecified","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.ddu.log.total","Total DDU log consumption","Sum of license consumption of Davis data units aggregated over all logs for the log pool","Unspecified","","auto|value",0
"builtin:billing.ddu.metrics.byEntity","DDU metrics consumption by monitored entity","License consumption of Davis data units by metrics pool split by monitored entity","Unspecified","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.ddu.metrics.byEntityRaw","DDU metrics consumption by monitored entity w/o host-unit included DDUs","License consumption of Davis data units by metrics pool split by monitored entity (aggregates host-unit included metrics; so value might be higher than actual consumption)","Unspecified","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.ddu.metrics.byMetric","Reported metrics DDUs by metric key","Reported Davis data units usage by metrics pool split by metric key","Unspecified","","auto|value",1
"builtin:billing.ddu.metrics.total","Total DDU metrics consumption","Sum of license consumption of Davis data units aggregated over all metrics for the metrics pool","Unspecified","","auto|value",0
"builtin:billing.ddu.serverless.byDescription","DDU serverless consumption by function","License consumption of Davis data units by serverless pool split by Amazon Resource Names (ARNs)","Unspecified","","auto|value",1
"builtin:billing.ddu.serverless.byEntity","DDU serverless consumption by service","License consumption of Davis data units by serverless pool split by service","Unspecified","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.ddu.serverless.total","Total DDU serverless consumption","Sum of license consumption of Davis data units aggregated over all services for the serverless pool","Unspecified","","auto|value",0
"builtin:billing.ddu.traces.byDescription","DDU traces consumption by span type","License consumption of Davis data units by traces pool split by SpanKind; as defined in OpenTelemetry specification","Unspecified","","auto|value",1
"builtin:billing.ddu.traces.byEntity","DDU traces consumption by monitored entity","License consumption of Davis data units by traces pool split by monitored entity","Unspecified","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.ddu.traces.total","Total DDU traces consumption","Sum of license consumption of Davis data units aggregated over all monitored entities for the traces pool","Unspecified","","auto|value",0
"builtin:billing.ddu.includedMetricDduPerHost","DDU included per host","Included Davis data units per host","Unspecified","HOST","auto|value",1
"builtin:billing.ddu.includedMetricPerHost","DDU included metric data points per host","Included metric data points per host","Unspecified","HOST","auto|value",1
"builtin:billing.events.business_events.ingest.usage","[Deprecated]  (DPS) Business events usage - Ingest & Process","Business events Ingest & Process usage; tracked as bytes ingested within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.","Byte","","auto|value",0
"builtin:billing.events.business_events.query.usage","[Deprecated] (DPS) Business events usage - Query","Business events Query usage; tracked as bytes read within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.","Byte","","auto|value",0
"builtin:billing.events.business_events.retain.usage","[Deprecated] (DPS) Business events usage - Retain","Business events Retain usage; tracked as total storage used within the hour; in bytes. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.","Byte","","auto|avg|max|min",0
"builtin:billing.foundation_and_discovery.metric_data_points.ingested","(DPS) Ingested metric data points for Foundation & Discovery","The number of metric data points aggregated over all Foundation & Discovery hosts.","Count","","auto|value",0
"builtin:billing.foundation_and_discovery.metric_data_points.ingested_by_host","(DPS) Ingested metric data points for Foundation & Discovery per host","The number of metric data points split by Foundation & Discovery hosts.","Count","HOST","auto|value",1
"builtin:billing.foundation_and_discovery.usage","(DPS) Foundation & Discovery billing usage","The total number of host-hours being monitored by Foundation & Discovery; counted in 15 min intervals.","Count","","auto|value",0
"builtin:billing.foundation_and_discovery.usage_per_host","(DPS) Foundation & Discovery billing usage per host","The host-hours being monitored by Foundation & Discovery; counted in 15 min intervals.","Count","HOST","auto|value",1
"builtin:billing.full_stack_monitoring.metric_data_points.included","(DPS) Available included metric data points for Full-Stack hosts","The total number of included metric data points that can be deducted from the metric data points reported by Full-Stack hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of applied included metric data points; use builtin:billing.full_stack_monitoring.metric_data_points.included_used . If the difference between this metric and the applied metrics is greater than 0; then more metrics can be ingested using Full-Stack Monitoring without incurring additional costs.","Count","","auto|value",0
"builtin:billing.full_stack_monitoring.metric_data_points.included_used","(DPS) Used included metric data points for Full-Stack hosts","The number of consumed included metric data points per host monitored with Full-Stack Monitoring. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of potentially available included metrics; use builtin:billing.full_stack_monitoring.metric_data_points.included_used . If the difference between this metric and the available metrics is greater than zero; then that means that more metrics could be ingested on Full-Stack hosts without incurring additional costs.","Count","","auto|value",0
"builtin:billing.full_stack_monitoring.metric_data_points.ingested","(DPS) Total metric data points reported by Full-Stack hosts","The number of metric data points aggregated over all Full-Stack hosts. The values reported in this metric are eligible for included-metric-data-point deduction. Use this total metric to query longer timeframes without losing precision or performance. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view usage on a per-host basis; use builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host .","Count","","auto|value",0
"builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host","(DPS) Metric data points reported and split by Full-Stack hosts","The number of metric data points split by Full-Stack hosts. The values reported in this metric are eligible for included-metric-data-point deduction. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. The pool of available included metrics for a "15-minute interval" is visible via builtin:billing.full_stack_monitoring.metric_data_points.included . To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","HOST","auto|value",1
"builtin:billing.full_stack_monitoring.usage","(DPS) Full-Stack Monitoring billing usage","The total GiB memory of hosts being monitored in full-stack mode; counted in 15 min intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the hosts causing the usage; refer to the usage_per_host metric. For details on the containers causing the usage; refer to the usage_per_container metric.","GibiByte","","auto|value",0
"builtin:billing.full_stack_monitoring.usage_per_container","(DPS) Full-stack usage by container type","The total GiB memory of containers being monitored in full-stack mode; counted in 15 min intervals.","GibiByte","","auto|value",5
"builtin:billing.full_stack_monitoring.usage_per_host","(DPS) Full-Stack Monitoring billing usage per host","The GiB memory per host being monitored in full-stack mode; counted in 15 min intervals. For example; a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","GibiByte","HOST","auto|value",1
"builtin:billing.infrastructure_monitoring.metric_data_points.included","(DPS) Available included metric data points for Infrastructure-monitored hosts","The total number of included metric data points that can be deducted from the metric data points reported by Infrastructure-monitored hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of applied included metric data points; use builtin:billing.infrastructure_monitoring.metric_data_points.included_used . If the difference between this metric and the applied metrics is greater than zero; then that means that more metrics could be ingested on Infrastructure-monitored hosts without incurring additional costs.","Count","","auto|value",0
"builtin:billing.infrastructure_monitoring.metric_data_points.included_used","(DPS) Used included metric data points for Infrastructure-monitored hosts","The number of consumed included metric data points for Infrastructure-monitored hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of potentially available included metrics; use builtin:billing.infrastructure_monitoring.metric_data_points.included_used . If the difference between this metric and the available metrics is greater than zero; then that means that more metrics could be ingested on Infrastructure-monitored hosts without incurring additional costs.","Count","","auto|value",0
"builtin:billing.infrastructure_monitoring.metric_data_points.ingested","(DPS) Total metric data points reported by Infrastructure-monitored hosts","The number of metric data points aggregated over all Infrastructure-monitored hosts. The values reported in this metric are eligible for included-metric-data-point deduction. Use this total metric to query longer timeframes without losing precision or performance. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view usage on a per-host basis; use the builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host .","Count","","auto|value",0
"builtin:billing.infrastructure_monitoring.metric_data_points.ingested_by_host","(DPS) Metric data points reported and split by Infrastructure-monitored hosts","The number of metric data points split by Infrastructure-monitored hosts. The values reported in this metric are eligible for included-metric-data-point deduction. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. The pool of available included metrics for a "15-minute interval" is visible via builtin:billing.infrastructure_monitoring.metric_data_points.included . To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","HOST","auto|value",1
"builtin:billing.infrastructure_monitoring.usage","(DPS) Infrastructure Monitoring billing usage","The total number of host-hours being monitored in infrastructure-only mode; counted in 15 min intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the hosts causing the usage; refer to the usage_per_host metric.","Count","","auto|value",0
"builtin:billing.infrastructure_monitoring.usage_per_host","(DPS) Infrastructure Monitoring billing usage per host","The host-hours being monitored in infrastructure-only mode; counted in 15 min intervals. A host monitored for the whole hour has 4 data points with a value of 0.25; regardless of the memory size. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","HOST","auto|value",1
"builtin:billing.kubernetes_monitoring.usage","(DPS) Kubernetes Platform Monitoring billing usage","The total number of monitored Kubernetes pods per hour; split by cluster and namespace and counted in 15 min intervals. A pod monitored for the whole hour has 4 data points with a value of 0.25.","Count","KUBERNETES_CLUSTER","auto|value",2
"builtin:billing.log.ingest.usage","(DPS) Log Management and Analytics usage - Ingest & Process","Log Management and Analytics Ingest & Process usage; tracked as bytes ingested within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.","Byte","","auto|value",0
"builtin:billing.log.query.usage","(DPS) Log Management and Analytics usage - Query","Log Management and Analytics Query usage; tracked as bytes read within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.","Byte","","auto|value",0
"builtin:billing.log.retain.usage","(DPS) Log Management and Analytics usage - Retain","Log Management and Analytics Retain usage; tracked as total storage used within the hour; in bytes. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.","Byte","","auto|avg|max|min",0
"builtin:billing.log_monitoring_classic.usage","(DPS) Total Log Monitoring Classic billing usage","The number of log records ingested aggregated over all monitored entities. A log record is recognized by either a timestamp or a JSON object. Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.log_monitoring_classic.usage_by_entity","(DPS) Log Monitoring Classic billing usage by monitored entity","The number of log records ingested split by monitored entity. A log record is recognized by either a timestamp or a JSON object. For details on the log path; refer to the usage_by_log_path metric. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.log_monitoring_classic.usage_by_log_path","(DPS) Log Monitoring Classic billing usage by log path","The number of log records ingested split by log path. A log record is recognized by either a timestamp or a JSON object. For details on the related monitored entities; refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","","auto|value",1
"builtin:billing.mainframe_monitoring.usage","(DPS) Mainframe Monitoring billing usage","The total number of MSU-hours being monitored; counted in 15 min intervals.","MSU","HOST","auto|value",1
"builtin:billing.real_user_monitoring.mobile.property.usage","(DPS) Total Real-User Monitoring Property (mobile) billing usage","(Mobile) User action and session properties count. For details on how usage is calculated; refer to the documentation or builtin:billing.real_user_monitoring.web.property.usage_by_application . Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.real_user_monitoring.mobile.property.usage_by_application","(DPS) Real-User Monitoring Property (mobile) billing usage by application","(Mobile) User action and session properties count by application. The billed value is calculated based on the number of sessions reported in builtin:billing.real_user_monitoring.mobile.session.usage_by_app + builtin:billing.real_user_monitoring.mobile.session_with_replay.usage_by_app . plus the number of configured properties that exceed the included number of properties (free of charge) offered for a given application. Data points are only written for billed sessions. If the value is 0; you have available metric data points. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",1
"builtin:billing.real_user_monitoring.mobile.session.usage","(DPS) Total Real-User Monitoring (mobile) billing usage","(Mobile) Session count without Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute; then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage; refer to the usage_by_app metric.","Count","","auto|value",0
"builtin:billing.real_user_monitoring.mobile.session.usage_by_app","(DPS) Real-User Monitoring (mobile) billing usage by application","(Mobile) Session count without Session Replay split by application The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute; then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",1
"builtin:billing.real_user_monitoring.mobile.session_with_replay.usage","(DPS) Total Real-User Monitoring (mobile) with Session Replay billing usage","(Mobile) Session count with Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute; then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage; refer to the usage_by_app metric.","Count","","auto|value",0
"builtin:billing.real_user_monitoring.mobile.session_with_replay.usage_by_app","(DPS) Real-User Monitoring (mobile) with Session Replay billing usage by application","(Mobile) Session count with Session Replay split by application. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute; then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","CUSTOM_APPLICATION|MOBILE_APPLICATION","auto|value",1
"builtin:billing.real_user_monitoring.web.property.usage","(DPS) Total Real-User Monitoring Property (web) billing usage","(Web) User action and session properties count. For details on how usage is calculated; refer to the documentation or builtin:billing.real_user_monitoring.web.property.usage_by_application . Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.real_user_monitoring.web.property.usage_by_application","(DPS) Real-User Monitoring Property (web) billing usage by application","(Web) User action and session properties count by application. The billed value is calculated based on the number of sessions reported in builtin:billing.real_user_monitoring.web.session.usage_by_app + builtin:billing.real_user_monitoring.web.session_with_replay.usage_by_app . plus the number of configured properties that exceed the included number of properties (free of charge) offered for a given application. Data points are only written for billed sessions. If the value is 0; you have available metric data points. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","APPLICATION","auto|value",1
"builtin:billing.real_user_monitoring.web.session.usage","(DPS) Total Real-User Monitoring (web) billing usage","(Web) Session count without Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute; then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage; refer to the usage_by_app metric.","Count","","auto|value",0
"builtin:billing.real_user_monitoring.web.session.usage_by_app","(DPS) Real-User Monitoring (web) billing usage by application","(Web) Session count without Session Replay split by application. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute; then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","APPLICATION","auto|value",1
"builtin:billing.real_user_monitoring.web.session_with_replay.usage","(DPS) Total Real-User Monitoring (web) with Session Replay billing usage","(Web) Session count with Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute; then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage; refer to the usage_by_app metric.","Count","","auto|value",0
"builtin:billing.real_user_monitoring.web.session_with_replay.usage_by_app","(DPS) Real-User Monitoring (web) with Session Replay billing usage by application","(Web) Session count with Session Replay split by application. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute; then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","APPLICATION","auto|value",1
"builtin:billing.runtime_application_protection.usage","(DPS) Runtime Application Protection billing usage","Total GiB-memory of hosts protected with Runtime Application Protection (Application Security); counted at 15-minute intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the monitored hosts; refer to the usage_per_host metric.","GibiByte","","auto|value",0
"builtin:billing.runtime_application_protection.usage_per_host","(DPS) Runtime Application Protection billing usage per host","GiB-memory per host protected with Runtime Application Protection (Application Security); counted at 15-minute intervals. For example; a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","GibiByte","HOST","auto|value",1
"builtin:billing.runtime_vulnerability_analytics.usage","(DPS) Runtime Vulnerability Analytics billing usage","Total GiB-memory of hosts protected with Runtime Vulnerability Analytics (Application Security); counted at 15-minute intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the monitored hosts; refer to the usage_per_host metric.","GibiByte","","auto|value",0
"builtin:billing.runtime_vulnerability_analytics.usage_per_host","(DPS) Runtime Vulnerability Analytics billing usage per host","GiB-memory per hosts protected with Runtime Vulnerability Analytics (Application Security); counted at 15-minute intervals. For example; a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","GibiByte","HOST","auto|value",1
"builtin:billing.serverless_functions_classic.usage","(DPS) Total Serverless Functions Classic billing usage","The number of invocations of the serverless function aggregated over all monitored entities. The term "function invocations" is equivalent to "function requests" or "function executions". Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.serverless_functions_classic.usage_by_entity","(DPS) Serverless Functions Classic billing usage by monitored entity","The number of invocations of the serverless function split by monitored entity. The term "function invocations" is equivalent to "function requests" or "function executions". For details on which functions are invoked; refer to the usage_by_function metric. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","APM_AGENT|APM_CLUSTER|APM_CLUSTER_NODE|APM_SECURITY_GATEWAY|APM_SERVER|APM_TENANT|APPLICATION|APPLICATION_METHOD|APPLICATION_METHOD_GROUP|APPMON_SERVER|APPMON_SYSTEM_PROFILE|AUTO_SCALING_GROUP|AWS_APPLICATION_LOAD_BALANCER|AWS_AVAILABILITY_ZONE|AWS_CREDENTIALS|AWS_LAMBDA_FUNCTION|AWS_NETWORK_LOAD_BALANCER|AZURE_API_MANAGEMENT_SERVICE|AZURE_APPLICATION_GATEWAY|AZURE_APP_SERVICE_PLAN|AZURE_COSMOS_DB|AZURE_CREDENTIALS|AZURE_EVENT_HUB|AZURE_EVENT_HUB_NAMESPACE|AZURE_FUNCTION_APP|AZURE_IOT_HUB|AZURE_LOAD_BALANCER|AZURE_MGMT_GROUP|AZURE_REDIS_CACHE|AZURE_REGION|AZURE_SERVICE_BUS_NAMESPACE|AZURE_SERVICE_BUS_QUEUE|AZURE_SERVICE_BUS_TOPIC|AZURE_SQL_DATABASE|AZURE_SQL_ELASTIC_POOL|AZURE_SQL_SERVER|AZURE_STORAGE_ACCOUNT|AZURE_SUBSCRIPTION|AZURE_TENANT|AZURE_VM|AZURE_VM_SCALE_SET|AZURE_WEB_APP|BOSH_DEPLOYMENT|BROWSER|CF_APPLICATION|CF_APPLICATION_INSTANCE|CF_FOUNDATION|CF_ORG|CF_SPACE|CINDER_VOLUME|CLOUD_APPLICATION|CLOUD_APPLICATION_INSTANCE|CLOUD_APPLICATION_NAMESPACE|CLOUD_NETWORK_INGRESS|CLOUD_NETWORK_POLICY|CONTAINER_GROUP|CONTAINER_GROUP_INSTANCE|CREDENTIALS_VAULT|CUSTOM_APPLICATION|CUSTOM_DEVICE|CUSTOM_DEVICE_GROUP|DATASTORE|DB_ENDPOINT|DB_ENDPOINT_GROUP|DCRUM_APPLICATION|DCRUM_SERVICE|DCRUM_SERVICE_INSTANCE|DEVICE_APPLICATION_METHOD|DEVICE_APPLICATION_METHOD_GROUP|DISK|DOCKER_CONTAINER_GROUP|DOCKER_CONTAINER_GROUP_INSTANCE|DYNAMO_DB_TABLE|EBS_VOLUME|EC2_INSTANCE|ELASTIC_LOAD_BALANCER|ENVIRONMENT|EXTENSION_TASK_CONFIGURATION|EXTERNAL_SYNTHETIC_TEST|EXTERNAL_SYNTHETIC_TEST_STEP|GCP_ZONE|GEOLOCATION|GEOLOC_SITE|GOOGLE_COMPUTE_ENGINE|GRAIL_BUSINESS_EVENTS_ANALYZE|GRAIL_BUSINESS_EVENTS_INGEST|GRAIL_BUSINESS_EVENTS_RETAIN|GRAIL_LOG_ANALYZE|GRAIL_LOG_INGEST|GRAIL_LOG_RETAIN|HOST|HOST_GROUP|HTTP_CHECK|HTTP_CHECK_STEP|HYPERVISOR|HYPERVISOR_CLUSTER|HYPERVISOR_DISK|KUBERNETES_CLUSTER|KUBERNETES_NODE|KUBERNETES_SERVICE|LOG_INSTANCE|MEASUREMENT|MEASUREMENT_GROUP|MOBILE_APPLICATION|MULTIPROTOCOL_MONITOR|NETWORK_INTERFACE|NEUTRON_SUBNET|OPENSTACK_AVAILABILITY_ZONE|OPENSTACK_COMPUTE_NODE|OPENSTACK_CREDENTIALS|OPENSTACK_PROJECT|OPENSTACK_REGION|OPENSTACK_VM|OS|PROCESS_GROUP|PROCESS_GROUP_INSTANCE|QUEUE|QUEUE_INSTANCE|RELATIONAL_DATABASE_SERVICE|REMOTE_PLUGIN_MODULE|REQUEST_ATTRIBUTE|RUNTIME_COMPONENT|S3BUCKET|SERVICE|SERVICE_INSTANCE|SERVICE_METHOD|SERVICE_METHOD_GROUP|SOFTWARE_COMPONENT|SWIFT_CONTAINER|SYNTHETIC_LOCATION|SYNTHETIC_TEST|SYNTHETIC_TEST_STEP|VCENTER|VIRTUALMACHINE|VMWARE_DATACENTER","auto|value",1
"builtin:billing.serverless_functions_classic.usage_by_function","(DPS) Serverless Functions Classic billing usage by function","The number of invocations of the serverless function split by function. The term "function invocations" is equivalent to "function requests" or "function executions". For details on the related monitored entities; refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","","auto|value",1
"builtin:billing.synthetic.actions","Actions","The number of billed actions consumed by browser monitors.","Count","SYNTHETIC_TEST","auto|value",2
"builtin:billing.synthetic.actions.usage","(DPS) Total Browser Monitor or Clickpath billing usage","The number of synthetic actions which triggers a web request that includes a page load; navigation event; or action that triggers an XHR or Fetch request. Scroll downs; keystrokes; or clicks that don't trigger web requests aren't counted as such actions. Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.synthetic.actions.usage_by_browser_monitor","(DPS) Browser Monitor or Clickpath billing usage per synthetic browser monitor","The number of synthetic actions which triggers a web request that includes a page load; navigation event; or action that triggers an XHR or Fetch request. Scroll downs; keystrokes; or clicks that don't trigger web requests aren't counted as such actions. Actions are split by the Synthetic Browser Monitors that caused them. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","SYNTHETIC_TEST","auto|value",1
"builtin:billing.synthetic.external","Third-party results","The number of billed results consumed by third-party monitors.","Count","EXTERNAL_SYNTHETIC_TEST","auto|value",2
"builtin:billing.synthetic.external.usage","(DPS) Total Third-Party Synthetic API Ingestion billing usage","The number of synthetic test results pushed into Dynatrace with Synthetic 3rd party API. Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.synthetic.external.usage_by_third_party_monitor","(DPS) Third-Party Synthetic API Ingestion billing usage per external browser monitor","The number of synthetic test results pushed into Dynatrace with Synthetic 3rd party API. The ingestions are split by external Synthetic Browser Monitors for which the results where ingested. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","EXTERNAL_SYNTHETIC_TEST","auto|value",1
"builtin:billing.synthetic.requests","Requests","The number of billed requests consumed by HTTP monitors.","Count","HTTP_CHECK","auto|value",2
"builtin:billing.synthetic.requests.usage","(DPS) Total HTTP monitor billing usage","The number of HTTP requests performed during execution of synthetic HTTP monitor. Use this total metric to query longer timeframes without losing precision or performance.","Count","","auto|value",0
"builtin:billing.synthetic.requests.usage_by_http_monitor","(DPS) HTTP monitor billing usage per HTTP monitor","The number of HTTP requests performed; split by synthetic HTTP monitor. To improve performance and avoid exceeding query limits when working with longer timeframes; use the total metric.","Count","HTTP_CHECK","auto|value",1
"builtin:cloud.aws.alb.connections.active","ALB number of active connections","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|avg|max|min",1
"builtin:cloud.aws.alb.connections.new","ALB number of new connections","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.errors.alb.http4xx","ALB number of 4XX errors","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.errors.alb.http5xx","ALB number of 5XX errors","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.errors.target.http4xx","ALB number of 4XX target errors","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.errors.target.http5xx","ALB number of 5XX target errors","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.errors.rejCon","ALB number of rejected connections","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.errors.targConn","ALB number of target connection errors","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.errors.tlsNeg","ALB number of client TLS negotiation errors","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.bytes","ALB number of processed bytes","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.lcus","ALB number of consumed LCUs","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.requests","ALB number of requests","","Count","AWS_APPLICATION_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.alb.respTime","ALB target response time","","Second","AWS_APPLICATION_LOAD_BALANCER","auto|avg|max|min",1
"builtin:cloud.aws.asg.running","Number of running EC2 instances (ASG)","","Count","AUTO_SCALING_GROUP","auto|avg|max|min",1
"builtin:cloud.aws.asg.stopped","Number of stopped EC2 instances (ASG)","","Count","AUTO_SCALING_GROUP","auto|avg|max|min",1
"builtin:cloud.aws.asg.terminated","Number of terminated EC2 instances (ASG)","","Count","AUTO_SCALING_GROUP","auto|avg|max|min",1
"builtin:cloud.aws.az.running","Number of running EC2 instances (AZ)","","Count","AWS_CREDENTIALS","auto|avg|max|min",2
"builtin:cloud.aws.az.stopped","Number of stopped EC2 instances (AZ)","","Count","AWS_CREDENTIALS","auto|avg|max|min",2
"builtin:cloud.aws.az.terminated","Number of terminated EC2 instances (AZ)","","Count","AWS_CREDENTIALS","auto|avg|max|min",2
"builtin:cloud.aws.dynamo.capacityUnits.consumed.read","DynamoDB read capacity units","","Count","DYNAMO_DB_TABLE","auto|value",1
"builtin:cloud.aws.dynamo.capacityUnits.consumed.write","DynamoDB write capacity units","","Count","DYNAMO_DB_TABLE","auto|value",1
"builtin:cloud.aws.dynamo.capacityUnits.provisioned.read","DynamoDB provisioned read capacity units","","Count","DYNAMO_DB_TABLE","auto|value",1
"builtin:cloud.aws.dynamo.capacityUnits.provisioned.write","DynamoDB provisioned write capacity units","","Count","DYNAMO_DB_TABLE","auto|value",1
"builtin:cloud.aws.dynamo.capacityUnits.read","DynamoDB read capacity units %","","Percent","DYNAMO_DB_TABLE","auto|avg|max|min",1
"builtin:cloud.aws.dynamo.capacityUnits.write","DynamoDB write capacity units %","","Percent","DYNAMO_DB_TABLE","auto|avg|max|min",1
"builtin:cloud.aws.dynamo.errors.system","DynamoDB number of requests with HTTP 500 status code","","Count","DYNAMO_DB_TABLE","auto|value",1
"builtin:cloud.aws.dynamo.errors.user","DynamoDB number of requests with HTTP 400 status code","","Count","DYNAMO_DB_TABLE","auto|value",1
"builtin:cloud.aws.dynamo.requests.latency","DynamoDB number of successful request latency for operation","","MilliSecond","DYNAMO_DB_TABLE","auto|avg|max|min",2
"builtin:cloud.aws.dynamo.requests.returnedItems","DynamoDB number of items returned by operation","","Count","DYNAMO_DB_TABLE","auto|value",2
"builtin:cloud.aws.dynamo.requests.throttled","DynamoDB number of throttled requests for operation","","Count","DYNAMO_DB_TABLE","auto|value",2
"builtin:cloud.aws.dynamo.throttledEvents.read","DynamoDB number of read throttled events","","Count","DYNAMO_DB_TABLE","auto|value",1
"builtin:cloud.aws.dynamo.throttledEvents.write","DynamoDB number of write throttled events","","Count","DYNAMO_DB_TABLE","auto|value",1
"builtin:cloud.aws.dynamo.tables","Number of tables for AvailabilityZone","","Count","AWS_CREDENTIALS","auto|avg|max|min",2
"builtin:cloud.aws.ebs.latency.read","EBS volume read latency","","Second","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ebs.latency.write","EBS volume write latency","","Second","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ebs.ops.consumed","EBS volume consumed OPS","","PerSecond","EBS_VOLUME","auto|value",1
"builtin:cloud.aws.ebs.ops.read","EBS volume read OPS","","PerSecond","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ebs.ops.write","EBS volume write OPS","","PerSecond","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ebs.throughput.percent","EBS volume throughput %","","Percent","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ebs.throughput.read","EBS volume read throughput","","PerSecond","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ebs.throughput.write","EBS volume write throughput","","PerSecond","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ebs.idleTime","EBS volume idle time %","","Percent","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ebs.queue","EBS volume queue length","","Count","EBS_VOLUME","auto|avg|max|min",1
"builtin:cloud.aws.ec2.cpu.usage","EC2 CPU usage %","","Percent","EC2_INSTANCE","auto|avg|max|min",1
"builtin:cloud.aws.ec2.disk.readOps","EC2 instance storage read IOPS","","PerSecond","EC2_INSTANCE","auto|avg|max|min",1
"builtin:cloud.aws.ec2.disk.readRate","EC2 instance storage read rate","","KiloBytePerSecond","EC2_INSTANCE","auto|avg|max|min",1
"builtin:cloud.aws.ec2.disk.writeOps","EC2 instance storage write IOPS","","PerSecond","EC2_INSTANCE","auto|avg|max|min",1
"builtin:cloud.aws.ec2.disk.writeRate","EC2 instance storage write rate","","KiloBytePerSecond","EC2_INSTANCE","auto|avg|max|min",1
"builtin:cloud.aws.ec2.net.rx","EC2 network data received rate","","BytePerSecond","EC2_INSTANCE","auto|avg|max|min",1
"builtin:cloud.aws.ec2.net.tx","EC2 network data transmitted rate","","BytePerSecond","EC2_INSTANCE","auto|avg|max|min",1
"builtin:cloud.aws.elb.errors.backend.connection","CLB backend connection errors","","Count","ELASTIC_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.elb.errors.backend.http2xx","CLB number of backend 2XX errors","","Count","ELASTIC_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.elb.errors.backend.http3xx","CLB number of backend 3XX errors","","Count","ELASTIC_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.elb.errors.backend.http4xx","CLB number of backend 4XX errors","","Count","ELASTIC_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.elb.errors.backend.http5xx","CLB number of backend 5XX errors","","Count","ELASTIC_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.elb.errors.elb.http4xx","CLB number of 4XX errors","","Count","ELASTIC_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.elb.errors.elb.http5xx","CLB number of 5XX errors","","Count","ELASTIC_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.elb.errors.frontend","CLB frontend errors percentage","","Percent","ELASTIC_LOAD_BALANCER","auto|avg|max|min",1
"builtin:cloud.aws.elb.hosts.healthy","CLB number of healthy hosts","","Count","ELASTIC_LOAD_BALANCER","auto|avg|max|min",1
"builtin:cloud.aws.elb.hosts.unhealthy","CLB number of unhealthy hosts","","Count","ELASTIC_LOAD_BALANCER","auto|avg|max|min",1
"builtin:cloud.aws.elb.latency","CLB latency","","Second","ELASTIC_LOAD_BALANCER","auto|avg|max|min",1
"builtin:cloud.aws.elb.reqCompl","CLB number of completed requests","","Count","ELASTIC_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.lambda.concExecutions","LambdaFunction concurrent executions count","","Count","AWS_LAMBDA_FUNCTION","auto|avg|count|max|min|sum",1
"builtin:cloud.aws.lambda.duration","LambdaFunction code execution time.","","MilliSecond","AWS_LAMBDA_FUNCTION","auto|avg|count|max|min|sum",1
"builtin:cloud.aws.lambda.errors","LambdaFunction number of failed invocations with HTTP 4XX status code","","Count","AWS_LAMBDA_FUNCTION","auto|value",1
"builtin:cloud.aws.lambda.errorsRate","LambdaFunction rate of failed invocations to all invocations %","","Percent","AWS_LAMBDA_FUNCTION","auto|avg|max|min",1
"builtin:cloud.aws.lambda.invocations","LambdaFunction number of times a function is invoked","","Count","AWS_LAMBDA_FUNCTION","auto|value",1
"builtin:cloud.aws.lambda.provConcExecutions","LambdaFunction provisioned concurrent executions count","","Count","AWS_LAMBDA_FUNCTION","auto|value",1
"builtin:cloud.aws.lambda.provConcInvocations","LambdaFunction provisioned concurrency invocation count","","Count","AWS_LAMBDA_FUNCTION","auto|value",1
"builtin:cloud.aws.lambda.provConcSpilloverInvocations","LambdaFunction provisioned concurrency spillover invocation count","","Count","AWS_LAMBDA_FUNCTION","auto|value",1
"builtin:cloud.aws.lambda.throttlers","LambdaFunction throttled function invocation count","","Count","AWS_LAMBDA_FUNCTION","auto|value",1
"builtin:cloud.aws.nlb.flow.active","NLB number of active flows","","Count","AWS_NETWORK_LOAD_BALANCER","auto|avg|max|min",1
"builtin:cloud.aws.nlb.flow.new","NLB number of new flows","","Count","AWS_NETWORK_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.nlb.tcp.reset.client","NLB number of client resets","","Count","AWS_NETWORK_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.nlb.tcp.reset.elb","NLB number of resets","","Count","AWS_NETWORK_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.nlb.tcp.reset.target","NLB number of target resets","","Count","AWS_NETWORK_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.nlb.bytes","NLB number of processed bytes","","Count","AWS_NETWORK_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.nlb.lcus","NLB number of consumed LCUs","","Count","AWS_NETWORK_LOAD_BALANCER","auto|value",1
"builtin:cloud.aws.rds.cpu.usage","RDS CPU usage %","","Percent","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.latency.read","RDS read latency","","Second","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.latency.write","RDS write latency","","Second","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.memory.freeable","RDS freeable memory","","Byte","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.memory.swap","RDS swap usage","","Byte","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.net.rx","RDS network received throughput","","BytePerSecond","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.net.tx","RDS network transmitted throughput","","BytePerSecond","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.ops.read","RDS read IOPS","","PerSecond","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.ops.write","RDS write IOPS","","PerSecond","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.throughput.read","RDS read throughput","","BytePerSecond","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.throughput.write","RDS write throughput","","BytePerSecond","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.connections","RDS connections","","Count","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.free","RDS free storage space %","","Percent","RELATIONAL_DATABASE_SERVICE","auto|avg|max|min",1
"builtin:cloud.aws.rds.restarts","RDS restarts","","Count","RELATIONAL_DATABASE_SERVICE","auto|value",1
"builtin:cloud.azure.apiMgmt.requests.failed","Failed requests","","Count","AZURE_API_MANAGEMENT_SERVICE","auto|avg|count|max|min|sum",3
"builtin:cloud.azure.apiMgmt.requests.other","Other requests","","Count","AZURE_API_MANAGEMENT_SERVICE","auto|avg|count|max|min|sum",3
"builtin:cloud.azure.apiMgmt.requests.successful","Successful requests","","Count","AZURE_API_MANAGEMENT_SERVICE","auto|avg|count|max|min|sum",3
"builtin:cloud.azure.apiMgmt.requests.total","Total requests","","Count","AZURE_API_MANAGEMENT_SERVICE","auto|avg|count|max|min|sum",3
"builtin:cloud.azure.apiMgmt.requests.unauth","Unauthorized requests","","Count","AZURE_API_MANAGEMENT_SERVICE","auto|avg|count|max|min|sum",3
"builtin:cloud.azure.apiMgmt.capacity","Capacity","","Percent","AZURE_API_MANAGEMENT_SERVICE","auto|avg|max|min",2
"builtin:cloud.azure.apiMgmt.duration","Duration","","MilliSecond","AZURE_API_MANAGEMENT_SERVICE","auto|avg|max|min",3
"builtin:cloud.azure.appGateway.backend.settings.pool.host.healthy","Healthy host count","","Count","AZURE_APPLICATION_GATEWAY","auto|avg|max|min",2
"builtin:cloud.azure.appGateway.backend.settings.pool.host.unhealthy","Unhealthy host count","","Count","AZURE_APPLICATION_GATEWAY","auto|avg|max|min",2
"builtin:cloud.azure.appGateway.backend.settings.traffic.requests.failed","Requests failed","","Count","AZURE_APPLICATION_GATEWAY","auto|value",2
"builtin:cloud.azure.appGateway.backend.settings.traffic.requests.total","Requests total","","Count","AZURE_APPLICATION_GATEWAY","auto|value",2
"builtin:cloud.azure.appGateway.http.status.response","Response status","","Count","AZURE_APPLICATION_GATEWAY","auto|value",2
"builtin:cloud.azure.appGateway.network.connections.count","Current connections count","","Count","AZURE_APPLICATION_GATEWAY","auto|avg|max|min",1
"builtin:cloud.azure.appGateway.network.throughput","Network throughput","","BytePerSecond","AZURE_APPLICATION_GATEWAY","auto|avg|max|min",1
"builtin:cloud.azure.appService.applicationQueue.requests","Requests in application queue","","Count","AZURE_WEB_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.functions.applicationQueue.requests","Requests in application queue","","Count","AZURE_FUNCTION_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.functions.execution.count","Function execution count","","Count","AZURE_FUNCTION_APP","auto|value",2
"builtin:cloud.azure.appService.functions.execution.unitsCount","Function execution units count","","Count","AZURE_FUNCTION_APP","auto|value",2
"builtin:cloud.azure.appService.functions.http.status.http5xx","HTTP 5xx","","Count","AZURE_FUNCTION_APP","auto|value",2
"builtin:cloud.azure.appService.functions.io.operations.other","IO other operations/s","","PerSecond","AZURE_FUNCTION_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.functions.io.operations.read","IO read operations/s","","PerSecond","AZURE_FUNCTION_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.functions.io.operations.write","IO write operations/s","","PerSecond","AZURE_FUNCTION_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.functions.io.other","IO other bytes/s","","BytePerSecond","AZURE_FUNCTION_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.functions.io.read","IO read bytes/s","","BytePerSecond","AZURE_FUNCTION_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.functions.io.write","IO write bytes/s","","BytePerSecond","AZURE_FUNCTION_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.functions.traffic.bytesReceived","Received bytes","","Byte","AZURE_FUNCTION_APP","auto|value",2
"builtin:cloud.azure.appService.functions.traffic.bytesSent","Sent bytes","","Byte","AZURE_FUNCTION_APP","auto|value",2
"builtin:cloud.azure.appService.http.status.http2xx","HTTP 2xx","","Count","AZURE_WEB_APP","auto|value",2
"builtin:cloud.azure.appService.http.status.http403","HTTP 403","","Count","AZURE_WEB_APP","auto|value",2
"builtin:cloud.azure.appService.http.status.http5xx","HTTP 5xx","","Count","AZURE_WEB_APP","auto|value",2
"builtin:cloud.azure.appService.io.operations.other","IO other operations/s","","PerSecond","AZURE_WEB_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.io.operations.read","IO read operations/s","","PerSecond","AZURE_WEB_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.io.operations.write","IO write operations/s","","PerSecond","AZURE_WEB_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.io.other","IO other bytes/s","","BytePerSecond","AZURE_WEB_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.io.read","IO read bytes/s","","BytePerSecond","AZURE_WEB_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.io.write","IO write bytes/s","","BytePerSecond","AZURE_WEB_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.response.avg","Response time avg","","Second","AZURE_WEB_APP","auto|avg|max|min",2
"builtin:cloud.azure.appService.traffic.bytesReceived","Received bytes","","Byte","AZURE_WEB_APP","auto|value",2
"builtin:cloud.azure.appService.traffic.bytesSent","Sent bytes","","Byte","AZURE_WEB_APP","auto|value",2
"builtin:cloud.azure.appService.traffic.requests","Requests count","","Count","AZURE_WEB_APP","auto|value",2
"builtin:cloud.azure.cosmos.availableStorage","Available Storage","","Byte","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.dataUsage","Data Usage","","Byte","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.documentCount","Document Count","","Count","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.documentQuota","Document Quota","","Byte","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.indexUsage","Index Usage","","Byte","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.metadataRequests","Metadata Requests","","Count","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.normalizedRUConsumption","Normalized request units consumption","","Percent","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.provisionedThroughput","Provisioned Throughput","","Count","AZURE_COSMOS_DB","auto|avg|max|min",3
"builtin:cloud.azure.cosmos.replicationLatency","Replication Latency","","MilliSecond","AZURE_COSMOS_DB","auto|avg|max|min",3
"builtin:cloud.azure.cosmos.requestUnits","Total number of request units","","Count","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.requests","Total number of requests","","Count","AZURE_COSMOS_DB","auto|avg|max|min",5
"builtin:cloud.azure.cosmos.serviceAvailability","Service Availability","","Percent","AZURE_COSMOS_DB","auto|avg|max|min",1
"builtin:cloud.azure.eventHub.capture.backlog","Capture backlog","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.capture.bytes","Captured bytes","","Byte","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.capture.msg","Captured messages","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.errors.quotaExceeded","Quota exceeded errors","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.errors.server","Server errors","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.errors.user","User errors","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.requests.incoming","Incoming requests","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.requests.successful","Successful requests","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.requests.throttled","Throttled requests","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.traffic.bytesIn","Incoming bytes","","BytePerMinute","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.traffic.bytesOut","Outgoing bytes","","Byte","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.traffic.msgIn","Incoming messages","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHub.traffic.msgOut","Outgoing messages","","Count","AZURE_EVENT_HUB","auto|value",1
"builtin:cloud.azure.eventHubNamespace.connections.active","Active connections","","Count","AZURE_EVENT_HUB_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.eventHubNamespace.connections.closed","Closed connections","","Count","AZURE_EVENT_HUB_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.eventHubNamespace.connections.opened","Opened connections","","Count","AZURE_EVENT_HUB_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.command.abandoned","Commands abandoned","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.command.completed","Commands completed","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.command.rejected","Commands rejected","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.device.connected","Connected devices","","Count","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.device.dailyThroughputThrottling","Number of throttling errors","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.device.dataUsage","Total device data usage","","Byte","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.device.registered","Total devices","","Count","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.eventHub.builtInEventHub.messages.delivered","Messages delivered to the built-in endpoint (messages/events)","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.eventHub.builtInEventHub.averageLatencyMs","Message latency for the built-in endpoint (messages/events)","","MilliSecond","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.eventHub.messages.delivered","Messages delivered to Event Hub endpoints","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.eventHub.averageLatencyMs","Message latency for event hub endpoints","","MilliSecond","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.messages.dropped","Dropped messages","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.messages.invalidForAllEndpoints","Invalid messages","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.messages.orphaned","Orphaned messages","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.messages.sendAttempts","Telemetry message send attempts","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.messages.sent","Telemetry messages sent","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.messages.sentToFallback","Messages matching fallback condition","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.serviceBus.queues.averageLatencyMs","Message latency for service bus queue endpoints","","MilliSecond","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.serviceBus.queues.messagesDelivered","Messages delivered to service bus queue endpoints","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.serviceBus.topics.averageLatencyMs","Message latency for service bus topic endpoints","","MilliSecond","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.serviceBus.topics.messagesDelivered","Messages delivered to service bus topic endpoints","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.storageEndpoints.avgLatencyMs","Message latency for storage endpoints","","MilliSecond","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.storageEndpoints.blobsWritten","Blobs written to storage","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.iotHub.storageEndpoints.bytesWritten","Data written to storage","","Byte","AZURE_IOT_HUB","auto|avg|max|min",1
"builtin:cloud.azure.iotHub.storageEndpoints.messageDelivered","Messages delivered to storage endpoints","","Count","AZURE_IOT_HUB","auto|value",1
"builtin:cloud.azure.loadbalancer.availability.dipTcp","Load balancer DIP TCP availability","","Percent","AZURE_LOAD_BALANCER","auto|avg|max|min",2
"builtin:cloud.azure.loadbalancer.availability.dipUdp","Load balancer DIP UDP availability","","Percent","AZURE_LOAD_BALANCER","auto|avg|max|min",2
"builtin:cloud.azure.loadbalancer.availability.vip","Load Balancer VIP availability","","Percent","AZURE_LOAD_BALANCER","auto|avg|max|min",2
"builtin:cloud.azure.loadbalancer.snatConnection.est","SNAT connections successful","","Count","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.loadbalancer.snatConnection.pending","SNAT connections pending","","Count","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.loadbalancer.snatConnection.rej","SNAT connections failed","","Count","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.loadbalancer.traffic.byteIn","Bytes received","","Byte","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.loadbalancer.traffic.byteOut","Bytes sent","","Byte","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.loadbalancer.traffic.packetIn","Packets received","","Count","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.loadbalancer.traffic.packetOut","Packets sent","","Count","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.loadbalancer.traffic.packetSynIn","SYN packets received","","Count","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.loadbalancer.traffic.packetSynOut","SYN packets sent","","Count","AZURE_LOAD_BALANCER","auto|value",2
"builtin:cloud.azure.redis.cache.hits","Cache hits","","Count","AZURE_REDIS_CACHE","auto|value",1
"builtin:cloud.azure.redis.cache.misses","Cache misses","","Count","AZURE_REDIS_CACHE","auto|value",1
"builtin:cloud.azure.redis.cache.read","Read bytes/s","","BytePerSecond","AZURE_REDIS_CACHE","auto|avg|max|min",1
"builtin:cloud.azure.redis.cache.write","Write bytes/s","","BytePerSecond","AZURE_REDIS_CACHE","auto|avg|max|min",1
"builtin:cloud.azure.redis.commands.get","Get commands","","Count","AZURE_REDIS_CACHE","auto|value",1
"builtin:cloud.azure.redis.commands.set","Set commands","","Count","AZURE_REDIS_CACHE","auto|value",1
"builtin:cloud.azure.redis.commands.total","Total no. of processed commands","","Count","AZURE_REDIS_CACHE","auto|value",1
"builtin:cloud.azure.redis.keys.evicted","No. of evicted keys","","Count","AZURE_REDIS_CACHE","auto|value",1
"builtin:cloud.azure.redis.keys.expired","No. of expired keys","","Count","AZURE_REDIS_CACHE","auto|value",1
"builtin:cloud.azure.redis.keys.total","Total no. of keys","","Count","AZURE_REDIS_CACHE","auto|avg|max|min",1
"builtin:cloud.azure.redis.memory.used","Used memory","","Byte","AZURE_REDIS_CACHE","auto|avg|max|min",1
"builtin:cloud.azure.redis.memory.usedRss","Used memory RSS","","Byte","AZURE_REDIS_CACHE","auto|avg|max|min",1
"builtin:cloud.azure.redis.connected","Connected clients","","Count","AZURE_REDIS_CACHE","auto|avg|max|min",1
"builtin:cloud.azure.redis.load","Server load","","Percent","AZURE_REDIS_CACHE","auto|avg|max|min",1
"builtin:cloud.azure.redis.processorTime","Processor time","","Percent","AZURE_REDIS_CACHE","auto|avg|max|min",1
"builtin:cloud.azure.region.vms.initializing","Number of starting VMs in region","","Count","AZURE_SUBSCRIPTION","auto|avg|max|min",2
"builtin:cloud.azure.region.vms.running","Number of active VMs in region","","Count","AZURE_SUBSCRIPTION","auto|avg|max|min",2
"builtin:cloud.azure.region.vms.stopped","Number of stopped VMs in region","","Count","AZURE_SUBSCRIPTION","auto|avg|max|min",2
"builtin:cloud.azure.serviceBus.namespace.connections.active","Total active connections","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.namespace.errors.server","Server errors","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|value",1
"builtin:cloud.azure.serviceBus.namespace.errors.user","User errors","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|value",1
"builtin:cloud.azure.serviceBus.namespace.messages.count","Count of messages","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.namespace.messages.countActive","Count of active messages","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.namespace.messages.countDeadLettered","Count of dead-lettered messages","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.namespace.messages.countScheduled","Count of scheduled messages","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.namespace.messages.incoming","Incoming messages","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|value",1
"builtin:cloud.azure.serviceBus.namespace.messages.outgoing","Outgoing messages","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|value",1
"builtin:cloud.azure.serviceBus.namespace.requests.incoming","Incoming requests","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|value",1
"builtin:cloud.azure.serviceBus.namespace.requests.successful","Total successful requests","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|value",1
"builtin:cloud.azure.serviceBus.namespace.requests.throttled","Throttled requests","","Count","AZURE_SERVICE_BUS_NAMESPACE","auto|value",1
"builtin:cloud.azure.serviceBus.namespace.cpu","Service bus premium namespace CPU usage metric","","Percent","AZURE_SERVICE_BUS_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.namespace.memory","Service bus premium namespace memory usage metric","","Percent","AZURE_SERVICE_BUS_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.namespace.size","Service bus size","","Byte","AZURE_SERVICE_BUS_NAMESPACE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.queue.errors.server","Server errors","","Count","AZURE_SERVICE_BUS_QUEUE","auto|value",1
"builtin:cloud.azure.serviceBus.queue.errors.user","User errors","","Count","AZURE_SERVICE_BUS_QUEUE","auto|value",1
"builtin:cloud.azure.serviceBus.queue.messages.count","Count of messages in queue","","Count","AZURE_SERVICE_BUS_QUEUE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.queue.messages.countActive","Count of active messages in a queue","","Count","AZURE_SERVICE_BUS_QUEUE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.queue.messages.countDeadLettered","Count of dead-lettered messages in a queue","","Count","AZURE_SERVICE_BUS_QUEUE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.queue.messages.countScheduled","Count of scheduled messages in a queue","","Count","AZURE_SERVICE_BUS_QUEUE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.queue.messages.incoming","Incoming messages","","Count","AZURE_SERVICE_BUS_QUEUE","auto|value",1
"builtin:cloud.azure.serviceBus.queue.messages.outgoing","Outgoing messages","","Count","AZURE_SERVICE_BUS_QUEUE","auto|value",1
"builtin:cloud.azure.serviceBus.queue.requests.incoming","Incoming requests","","Count","AZURE_SERVICE_BUS_QUEUE","auto|value",1
"builtin:cloud.azure.serviceBus.queue.requests.successful","Total successful requests","","Count","AZURE_SERVICE_BUS_QUEUE","auto|value",1
"builtin:cloud.azure.serviceBus.queue.requests.throttled","Throttled requests","","Count","AZURE_SERVICE_BUS_QUEUE","auto|value",1
"builtin:cloud.azure.serviceBus.queue.size","Size of an queue","","Byte","AZURE_SERVICE_BUS_QUEUE","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.topic.errors.server","Server errors","","Count","AZURE_SERVICE_BUS_TOPIC","auto|value",1
"builtin:cloud.azure.serviceBus.topic.errors.user","User errors","","Count","AZURE_SERVICE_BUS_TOPIC","auto|value",1
"builtin:cloud.azure.serviceBus.topic.messages.count","Count of messages in topic","","Count","AZURE_SERVICE_BUS_TOPIC","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.topic.messages.countActive","Count of active messages in a topic","","Count","AZURE_SERVICE_BUS_TOPIC","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.topic.messages.countDeadLettered","Count of dead-lettered messages in a topic","","Count","AZURE_SERVICE_BUS_TOPIC","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.topic.messages.countScheduled","Count of scheduled messages in a topic","","Count","AZURE_SERVICE_BUS_TOPIC","auto|avg|max|min",1
"builtin:cloud.azure.serviceBus.topic.messages.incoming","Incoming messages","","Count","AZURE_SERVICE_BUS_TOPIC","auto|value",1
"builtin:cloud.azure.serviceBus.topic.messages.outgoing","Outgoing messages","","Count","AZURE_SERVICE_BUS_TOPIC","auto|value",1
"builtin:cloud.azure.serviceBus.topic.requests.incoming","Incoming requests","","Count","AZURE_SERVICE_BUS_TOPIC","auto|value",1
"builtin:cloud.azure.serviceBus.topic.requests.successful","Total successful requests","","Count","AZURE_SERVICE_BUS_TOPIC","auto|value",1
"builtin:cloud.azure.serviceBus.topic.requests.throttled","Throttled requests","","Count","AZURE_SERVICE_BUS_TOPIC","auto|value",1
"builtin:cloud.azure.serviceBus.topic.size","Size of a topic","","Byte","AZURE_SERVICE_BUS_TOPIC","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.connections.blockedByFirewall","Blocked by firewall","","Count","AZURE_SQL_DATABASE","auto|value",1
"builtin:cloud.azure.sqlDatabase.connections.failed","Failed connections","","Count","AZURE_SQL_DATABASE","auto|value",1
"builtin:cloud.azure.sqlDatabase.connections.successful","Successful connections","","Count","AZURE_SQL_DATABASE","auto|value",1
"builtin:cloud.azure.sqlDatabase.dtu.limit.count","DTU limit","","Count","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.dtu.limit.used","DTU used","","Count","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.dtu.consumptionPerc","DTU percentage","","Percent","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.io.dataRead","Data I/O percentage","","Percent","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.io.logWrite","Log I/O percentage","","Percent","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.storage.percent","Database size percentage","","Percent","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.storage.totalBytes","Total database size","","Byte","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.storage.xtpPercent","In-Memory OLTP storage percent","","Percent","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.cpuPercent","CPU percentage","","Percent","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.deadlocks","Deadlocks","","Count","AZURE_SQL_DATABASE","auto|value",1
"builtin:cloud.azure.sqlDatabase.sessions","Sessions percentage","","Percent","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlDatabase.workers","Workers percentage","","Percent","AZURE_SQL_DATABASE","auto|avg|max|min",1
"builtin:cloud.azure.sqlElasticPool.dtu.storage.limitBytes","Storage limit","","Byte","AZURE_SQL_ELASTIC_POOL","auto|avg|max|min",1
"builtin:cloud.azure.sqlElasticPool.dtu.storage.percent","Database size percentage","","Percent","AZURE_SQL_ELASTIC_POOL","auto|avg|max|min",1
"builtin:cloud.azure.sqlElasticPool.dtu.storage.usedBytes","Storage used","","Byte","AZURE_SQL_ELASTIC_POOL","auto|avg|max|min",1
"builtin:cloud.azure.sqlElasticPool.dtu.storage.xtpPercent","In-memory OLTP storage percent","","Percent","AZURE_SQL_ELASTIC_POOL","auto|avg|max|min",1
"builtin:cloud.azure.sqlElasticPool.dtu.consumption","DTU percentage","","Percent","AZURE_SQL_ELASTIC_POOL","auto|avg|max|min",1
"builtin:cloud.cloudfoundry.http.badGateways","CF: 502 responses","The number of responses that indicate invalid service responses produced by an application.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:cloud.cloudfoundry.http.latency","CF: Response latency","The average response time from the application to clients.","MilliSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:cloud.cloudfoundry.http.responses5xx","CF: 5xx responses","The number of responses that indicate repeatedly crashing apps or response issues from applications.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:cloud.cloudfoundry.http.totalRequests","CF: Total requests","The number of all requests representing the overall traffic flow.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:containers.cpu.limit","Containers: CPU limit; mCores","CPU resource limit per container in millicores.","MilliCores","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.logicalCores","Containers: CPU logical cores","Number of logical CPU cores of the host.","Cores","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.shares","Containers: CPU shares","Number of CPU shares allocated per container.","Count","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.throttledMilliCores","Containers: CPU throttling; mCores","CPU throttling per container in millicores.","MilliCores","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.throttledTime","Containers: CPU throttled time; ns/min","Total amount of time a container has been throttled; in nanoseconds per minute.","NanoSecondPerMinute","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.usageMilliCores","Containers: CPU usage; mCores","CPU usage per container in millicores","MilliCores","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.usagePercent","Containers: CPU usage; % of limit","Percent CPU usage per container relative to CPU resource limit. Logical cores are used if CPU limit isn't set.","Percent","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.usageSystemMilliCores","Containers: CPU system usage; mCores","CPU system usage per container in millicores.","MilliCores","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.usageSystemTime","Containers: CPU system usage time; ns/min","Used system time per container in nanoseconds per minute.","NanoSecondPerMinute","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.usageTime","Containers: CPU usage time; ns/min","Sum of used system and user time per container in nanoseconds per minute.","NanoSecondPerMinute","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.usageUserMilliCores","Containers: CPU user usage; mCores","CPU user usage per container in millicores.","MilliCores","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.cpu.usageUserTime","Containers: CPU user usage time; ns/min","Used user time per container in nanoseconds per minute.","NanoSecondPerMinute","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.memory.cacheBytes","Containers: Memory cache; bytes","Page cache memory per container in bytes.","Byte","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.memory.limitBytes","Containers: Memory limit; bytes","Memory limit per container in bytes. If no limit is set; this is an empty value.","Byte","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.memory.limitPercent","Containers: Memory limit; % of physical memory","Percent memory limit per container relative to total physical memory. If no limit is set; this is an empty value.","Percent","CONTAINER_GROUP_INSTANCE","auto|avg",2
"builtin:containers.memory.physicalTotalBytes","Containers: Memory - total physical memory; bytes","Total physical memory on the host in bytes.","Byte","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.memory.residentSetBytes","Containers: Memory usage; bytes","Resident set size (Linux) or private working set size (Windows) per container in bytes.","Byte","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:containers.memory.usagePercent","Containers: Memory usage; % of limit","Resident set size (Linux) or private working set size (Windows) per container in percent relative to container memory limit. If no limit is set; this equals total physical memory.","Percent","CONTAINER_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:host.availability","Host availability %","Host availability %","Percent","HOST","auto|avg",1
"builtin:host.availability.state","Host availability","Host availability state metric reported in 1 minute intervals","Count","HOST","auto|value",3
"builtin:host.cpu.idle","CPU idle","Average CPU time; when the CPU didn't have anything to do","Percent","HOST","auto|avg|max|min",1
"builtin:host.cpu.iowait","CPU I/O wait","Percentage of time when CPU was idle during which the system had an outstanding I/O request. It is not available on Windows.","Percent","HOST","auto|avg|max|min",1
"builtin:host.cpu.load","System load","The average number of processes that are being executed by CPU or waiting to be executed by CPU over the last minute","Ratio","HOST","auto|avg|max|min",1
"builtin:host.cpu.load15m","System load15m","The average number of processes that are being executed by CPU or waiting to be executed by CPU over the last 15 minutes","Ratio","HOST","auto|avg|max|min",1
"builtin:host.cpu.load5m","System load5m","The average number of processes that are being executed by CPU or waiting to be executed by CPU over the last 5 minutes","Ratio","HOST","auto|avg|max|min",1
"builtin:host.cpu.other","CPU other","Average CPU time spent on other tasks: servicing interrupt requests (IRQ); running virtual machines under the control of the host's kernel (meaning the host is a hypervisor for VMs). It's available only for Linux hosts","Percent","HOST","auto|avg|max|min",1
"builtin:host.cpu.steal","CPU steal","Average CPU time; when a virtual machine waits to get CPU cycles from the hypervisor. In a virtual environment; CPU cycles are shared across virtual machines on the hypervisor server. If your virtualized host displays a high CPU steal; it means CPU cycles are being taken away from your virtual machine to serve other purposes. It may indicate an overloaded hypervisor. It's available only for Linux hosts","Percent","HOST","auto|avg|max|min",1
"builtin:host.cpu.system","CPU system","Average CPU time when CPU was running in kernel mode","Percent","HOST","auto|avg|max|min",1
"builtin:host.cpu.usage","CPU usage %","Percentage of CPU time when CPU was utilized. A value close to 100% means most host processing resources are in use; and host CPUs can't handle additional work","Percent","HOST","auto|avg|max|min",1
"builtin:host.cpu.user","CPU user","Average CPU time when CPU was running in user mode","Percent","HOST","auto|avg|max|min",1
"builtin:host.disk.throughput.read","Disk throughput read","File system read throughput in bits per second","BitPerSecond","HOST","auto|avg|max|min",2
"builtin:host.disk.throughput.write","Disk throughput write","File system write throughput in bits per second","BitPerSecond","HOST","auto|avg|max|min",2
"builtin:host.disk.avail","Disk available","Amount of free space available for user in file system. On Linux and AIX it is free space available for unprivileged user. It doesn't contain part of free space reserved for the root.","Byte","HOST","auto|avg|max|min",2
"builtin:host.disk.bytesRead","Disk read bytes per second","Speed of read from file system in bytes per second","BytePerSecond","HOST","auto|avg|max|min",2
"builtin:host.disk.bytesWritten","Disk write bytes per second","Speed of write to file system in bytes per second","BytePerSecond","HOST","auto|avg|max|min",2
"builtin:host.disk.free","Disk available %","Percentage of free space available for user in file system. On Linux and AIX it is % of free space available for unprivileged user. It doesn't contain part of free space reserved for the root.","Percent","HOST","auto|avg|max|min",2
"builtin:host.disk.inodesAvail","Inodes available %","Percentage of free inodes available for unprivileged user in file system. Metric not available on Windows.","Percent","HOST","auto|avg|max|min",2
"builtin:host.disk.inodesTotal","Inodes total","Total amount of inodes available for unprivileged user in file system. Metric not available on Windows.","Count","HOST","auto|avg|max|min",2
"builtin:host.disk.queueLength","Disk average queue length","Average number of read and write operations in disk queue","Count","HOST","auto|avg|max|min",2
"builtin:host.disk.readOps","Disk read operations per second","Number of read operations from file system per second","PerSecond","HOST","auto|avg|max|min",2
"builtin:host.disk.readTime","Disk read time","Average time of read from file system. It shows average disk latency during read.","MilliSecond","HOST","auto|avg|count|max|min|sum",2
"builtin:host.disk.used","Disk used","Amount of used space in file system","Byte","HOST","auto|avg|max|min",2
"builtin:host.disk.usedPct","Disk used %","Percentage of used space in file system","Percent","HOST","auto|avg|max|min",2
"builtin:host.disk.utilTime","Disk utilization time","Percent of time spent on disk I/O operations","Percent","HOST","auto|avg|max|min",2
"builtin:host.disk.writeOps","Disk write operations per second","Number of write operations to file system per second","PerSecond","HOST","auto|avg|max|min",2
"builtin:host.disk.writeTime","Disk write time","Average time of write to file system. It shows average disk latency during write.","MilliSecond","HOST","auto|avg|count|max|min|sum",2
"builtin:host.dns.errorCount","Number of DNS errors by type","Number of DNS errors by type","Count","HOST","auto|avg|count|max|min|sum",3
"builtin:host.dns.queryCount","Number of DNS queries","Number of DNS queries on the host","Count","HOST","auto|avg|count|max|min|sum",2
"builtin:host.dns.queryTime","DNS query time sum","The time of all DNS queries on the host","MilliSecond","HOST","auto|avg|count|max|min|sum",2
"builtin:host.dns.singleQueryTime","DNS query time","Average time of DNS query. Calculated with DNS query time sum divided by number of DNS queries for each host and DNS server pair.","MilliSecond","HOST","auto|avg|max|min",2
"builtin:host.dns.singleQueryTimeByDnsIp","DNS query time by DNS server","The weighted average time of DNS query by DNS server ip. Calculated with DNS query time sum divided by number of DNS queries. It weights the result taking into account number of requests from each host.","MilliSecond","HOST","auto|avg|max|min",1
"builtin:host.dns.singleQueryTimeByHost","DNS query time on host","The weighted average time of DNS query on a host. Calculated with DNS query time sum divided by number of DNS queries on a host. It weights the result taking into account number of requests to each dns server","MilliSecond","HOST","auto|avg|max|min",1
"builtin:host.handles.fileDescriptorsMax","File descriptors max","Maximum amount of file descriptors for use","Count","HOST","auto|avg|max|min",1
"builtin:host.handles.fileDescriptorsUsed","File descriptors used","Amount of file descriptors used","Count","HOST","auto|avg|max|min",1
"builtin:host.mem.avail.bytes","Memory available","The amount of memory (RAM) available on the host. The memory that is available for allocation to new or existing processes. Available memory is an estimation of how much memory is available for use without swapping.","Byte","HOST","auto|avg|max|min",1
"builtin:host.mem.avail.pct","Memory available %","The percentage of memory (RAM) available on the host. The memory that is available for allocation to new or existing processes. Available memory is an estimation of how much memory is available for use without swapping. Shows available memory as percentages.","Percent","HOST","auto|avg|max|min",1
"builtin:host.mem.avail.pfps","Page faults per second","The measure of the number of page faults per second on the monitored host. This value includes soft faults and hard faults.","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.mem.swap.avail","Swap available","The amount of swap memory or swap space (also known as paging; which is the on-disk component of the virtual memory system) available.","Byte","HOST","auto|avg|max|min",1
"builtin:host.mem.swap.total","Swap total","Amount of total swap memory or total swap space (also known as paging; which is the on-disk component of the virtual memory system) for use.","Byte","HOST","auto|value",1
"builtin:host.mem.swap.used","Swap used","The amount of swap memory or swap space (also known as paging; which is the on-disk component of the virtual memory system) used.","Byte","HOST","auto|avg|max|min",1
"builtin:host.mem.recl","Memory reclaimable","The memory usage for specific purposes. Reclaimable memory is calculated as available memory (estimation of how much memory is available for use without swapping) minus free memory (amount of memory that is currently not used for anything). For more information on reclaimable memory; see [this blog post](https://www.dynatrace.com/news/blog/improved-host-memory-metrics-now-include-reclaimable-memory/).","Byte","HOST","auto|avg|max|min",1
"builtin:host.mem.total","Memory total","The amount of memory (RAM) installed on the system.","Byte","HOST","auto|value",1
"builtin:host.mem.usage","Memory used %","Shows percentage of memory currently used. Used memory is calculated by OneAgent as follows: used = total - available. So the used memory metric displayed in Dynatrace analysis views is not equal to the used memory metric displayed by system tools. At the same time; it's important to remember that system tools report used memory the way they do due to historical reasons; and that this particular method of calculating used memory isn't really representative of how the Linux kernel manages memory in modern systems. The difference in these measurements is in fact quite significant; too. Note: Calculated by taking 100% - "Memory available %".","Percent","HOST","auto|avg|max|min",1
"builtin:host.mem.used","Memory used","Used memory is calculated by OneAgent as follows: used = total - available. So the used memory metric displayed in Dynatrace analysis views is not equal to the used memory metric displayed by system tools. At the same time; it's important to remember that system tools report used memory the way they do due to historical reasons; and that this particular method of calculating used memory isn't really representative of how the Linux kernel manages memory in modern systems. The difference in these measurements is in fact quite significant; too.","Byte","HOST","auto|avg|max|min",1
"builtin:host.net.nic.packets.dropped","NIC packets dropped","Network interface packets dropped on the host","PerSecond","HOST","auto|value",2
"builtin:host.net.nic.packets.droppedRx","NIC received packets dropped","Network interface received packets dropped on the host","PerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.packets.droppedTx","NIC sent packets dropped","Network interface sent packets dropped on the host","PerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.packets.errors","NIC packet errors","Network interface packet errors on the host","PerSecond","HOST","auto|value",2
"builtin:host.net.nic.packets.errorsRx","NIC received packet errors","Network interface received packet errors on a host","PerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.packets.errorsTx","NIC sent packet errors","Network interface sent packet errors on the host","PerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.packets.rx","NIC packets received","Network interface packets received on the host","PerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.packets.tx","NIC packets sent","Network interface packets sent on the host","PerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.bytesRx","NIC bytes received","Network interface bytes received on the host","BytePerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.bytesTx","NIC bytes sent on host","Network interface bytes sent on the host","BytePerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.connectivity","NIC connectivity","Network interface connectivity on the host","Percent","HOST","auto|avg|max|min",2
"builtin:host.net.nic.connectivityNew","NIC connectivity","Network interface connectivity on the host","Percent","","auto|avg|max|min",0
"builtin:host.net.nic.linkUtilRx","NIC receive link utilization","Network interface receive link utilization on the host","Percent","HOST","auto|avg|max|min",2
"builtin:host.net.nic.linkUtilTx","NIC transmit link utilization","Network interface transmit link utilization on the host","Percent","HOST","auto|avg|max|min",2
"builtin:host.net.nic.retransmission","NIC retransmission","Network interface retransmission on the host","Percent","HOST","auto|avg|max|min",2
"builtin:host.net.nic.retransmissionIn","NIC received packets retransmission","Network interface retransmission for received packets on the host","Percent","HOST","auto|avg|max|min",2
"builtin:host.net.nic.retransmissionOut","NIC sent packets retransmission","Network interface retransmission for sent packets on the host","Percent","HOST","auto|avg|max|min",2
"builtin:host.net.nic.traffic","Traffic","Network traffic on the host","BitPerSecond","HOST","auto|value",2
"builtin:host.net.nic.trafficIn","Traffic in","Traffic incoming at the host","BitPerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.nic.trafficOut","Traffic out","Traffic outgoing from the host","BitPerSecond","HOST","auto|avg|max|min",2
"builtin:host.net.packets.rxBaseReceived","Host retransmission base received","Host aggregated process retransmission base received per second","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.packets.rxBaseSent","Host retransmission base sent","Host aggregated process retransmission base sent per second","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.packets.rxReceived","Host retransmitted packets received","Host aggregated process retransmitted packets received per second","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.packets.rxSent","Host retransmitted packets sent","Host aggregated process retransmitted packets sent per second","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.sessions.local.errRst","Localhost session reset received","Host aggregated session reset received per second on localhost","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.sessions.local.errTmout","Localhost session timeout received","Host aggregated session timeout received per second on localhost","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.sessions.local.new","Localhost new session received","Host aggregated new session received per second on localhost","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.sessions.errRst","Host session reset received","Host aggregated process session reset received per second","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.sessions.errTmout","Host session timeout received","Host aggregated process session timeout received per second","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.sessions.new","Host new session received","Host aggregated process new session received per second","PerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.bytesRx","Host bytes received","Host aggregated process bytes received per second","BytePerSecond","HOST","auto|avg|max|min",1
"builtin:host.net.bytesTx","Host bytes sent","Host aggregated process bytes sent per second","BytePerSecond","HOST","auto|avg|max|min",1
"builtin:host.osProcessStats.osProcessCount","OS Process count","This metric shows an average number of processes; over one minute; running on the host. The reported number of processes is based on processes detected by the OS module; read in 10 seconds cycles.","Count","HOST","auto|avg|max|min",1
"builtin:host.osProcessStats.pgiCount","PGI count","This metric shows the number of PGIs created by the OS module every minute. It includes every PGI; even those which are considered not important and are not reported to Dynatrace.","Count","HOST","auto|avg|max|min",1
"builtin:host.osProcessStats.pgiReportedCount","Reported PGI count","This metric shows the number of PGIs created and reported by the OS module every minute. It includes only PGIs; which are considered important and reported to Dynatrace. Important PGIs are those in which OneAgent recognizes the technology; have open network ports; generate significant resource usage; or are created via Declarative process grouping rules. To learn what makes process important; see [Which are the most important processes?](https://dt-url.net/most-important-processes)","Count","HOST","auto|avg|max|min",1
"builtin:host.uptime","Host uptime","Time since last host boot up. Requires OneAgent 1.259+. The metric is not supported for application-only OneAgent deployments.","Second","HOST","auto|avg|max|min",2
"builtin:kubernetes.cluster.readyz","Kubernetes: Cluster readyz status","Current status of the Kubernetes API server reported by the /readyz endpoint (0 or 1).","Unspecified","KUBERNETES_CLUSTER","auto|avg|max|min",3
"builtin:kubernetes.container.oom_kills","Kubernetes: Container - out of memory (OOM) kill count","This metric measures the out of memory (OOM) kills. The most detailed level of aggregation is container. The value corresponds to the status 'OOMKilled' of a container in the pod resource's container status. The metric is only written if there was at least one container OOM kill.","Count","CLOUD_APPLICATION_INSTANCE","auto|value",20
"builtin:kubernetes.container.restarts","Kubernetes: Container - restart count","This metric measures the amount of container restarts. The most detailed level of aggregation is container. The value corresponds to the delta of the 'restartCount' defined in the pod resource's container status. The metric is only written if there was at least one container restart.","Count","CLOUD_APPLICATION_INSTANCE","auto|value",20
"builtin:kubernetes.node.conditions","Kubernetes: Node conditions","This metric describes the status of a Kubernetes node. The most detailed level of aggregation is node.","Count","KUBERNETES_NODE","auto|avg|max|min",9
"builtin:kubernetes.node.cpu_allocatable","Kubernetes: Node - CPU allocatable","This metric measures the total allocatable cpu. The most detailed level of aggregation is node. The value corresponds to the allocatable cpu of a node.","MilliCores","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.node.cpu_usage","Kubernetes: Container - CPU usage (by node)","This metric measures the total CPU consumed (user usage + system usage) by container. The most detailed level of aggregation is node.","MilliCores","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.node.limits_cpu","Kubernetes: Pod - CPU limits (by node)","This metric measures the cpu limits. The most detailed level of aggregation is node. The value is the sum of the cpu limits of all app containers of a pod.","MilliCores","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.node.limits_memory","Kubernetes: Pod - memory limits (by node)","This metric measures the memory limits. The most detailed level of aggregation is node. The value is the sum of the memory limits of all app containers of a pod.","Byte","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.node.memory_allocatable","Kubernetes: Node - memory allocatable","This metric measures the total allocatable memory. The most detailed level of aggregation is node. The value corresponds to the allocatable memory of a node.","Byte","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.node.memory_working_set","Kubernetes: Container - Working set memory (by node)","This metric measures the current working set memory (memory that cannot be reclaimed under pressure) by container. The OOM Killer is invoked if the working set exceeds the limit. The most detailed level of aggregation is node.","Byte","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.node.pods","Kubernetes: Pod count (by node)","This metric measures the number of pods. The most detailed level of aggregation is node. The value corresponds to the count of all pods.","Count","KUBERNETES_NODE","auto|avg|max|min",10
"builtin:kubernetes.node.pods_allocatable","Kubernetes: Node - pod allocatable count","This metric measures the total number of allocatable pods. The most detailed level of aggregation is node. The value corresponds to the allocatable pods of a node.","Count","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.node.requests_cpu","Kubernetes: Pod - CPU requests (by node)","This metric measures the cpu requests. The most detailed level of aggregation is node. The value is the sum of the cpu requests of all app containers of a pod.","MilliCores","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.node.requests_memory","Kubernetes: Pod - memory requests (by node)","This metric measures the memory requests. The most detailed level of aggregation is node. The value is the sum of the memory requests of all app containers of a pod.","Byte","KUBERNETES_NODE","auto|avg|max|min",6
"builtin:kubernetes.persistentvolumeclaim.available","Kubernetes: PVC - available","This metric measures the number of available bytes in the volume. The most detailed level of aggregation is persistent volume claim.","Byte","CLOUD_APPLICATION_NAMESPACE","auto|avg|max|min",7
"builtin:kubernetes.persistentvolumeclaim.capacity","Kubernetes: PVC - capacity","This metric measures the capacity in bytes of the volume. The most detailed level of aggregation is persistent volume claim.","Byte","CLOUD_APPLICATION_NAMESPACE","auto|avg|max|min",7
"builtin:kubernetes.persistentvolumeclaim.used","Kubernetes: PVC - used","This metric measures the number of used bytes in the volume. The most detailed level of aggregation is persistent volume claim.","Byte","CLOUD_APPLICATION_NAMESPACE","auto|avg|max|min",7
"builtin:kubernetes.resourcequota.pods","Kubernetes: Resource quota - pod count","This metric measures the pods quota. The most detailed level of aggregation is resource quota. The value corresponds to the pods of a resource quota.","Count","CLOUD_APPLICATION_NAMESPACE","auto|avg|max|min",6
"builtin:kubernetes.resourcequota.pods_used","Kubernetes: Resource quota - pod used count","This metric measures the used pods quota. The most detailed level of aggregation is resource quota. The value corresponds to the used pods of a resource quota.","Count","CLOUD_APPLICATION_NAMESPACE","auto|avg|max|min",6
"builtin:kubernetes.workload.conditions","Kubernetes: Workload conditions","This metric describes the status of a Kubernetes workload. The most detailed level of aggregation is workload.","Count","CLOUD_APPLICATION","auto|avg|max|min",13
"builtin:kubernetes.workload.containers_desired","Kubernetes: Pod - desired container count","This metric measures the number of desired containers. The most detailed level of aggregation is workload. The value is the count of all containers in the pod's specification.","Count","CLOUD_APPLICATION","auto|avg|max|min",15
"builtin:kubernetes.workload.cpu_usage","Kubernetes: Container - CPU usage (by workload)","This metric measures the total CPU consumed (user usage + system usage) by container. The most detailed level of aggregation is workload.","MilliCores","CLOUD_APPLICATION","auto|avg|max|min",15
"builtin:kubernetes.workload.limits_cpu","Kubernetes: Pod - CPU limits (by workload)","This metric measures the cpu limits. The most detailed level of aggregation is workload. The value is the sum of the cpu limits of all app containers of a pod.","MilliCores","CLOUD_APPLICATION","auto|avg|max|min",15
"builtin:kubernetes.workload.limits_memory","Kubernetes: Pod - memory limits (by workload)","This metric measures the memory limits. The most detailed level of aggregation is workload. The value is the sum of the memory limits of all app containers of a pod.","Byte","CLOUD_APPLICATION","auto|avg|max|min",15
"builtin:kubernetes.workload.memory_working_set","Kubernetes: Container - Working set memory (by workload)","This metric measures the current working set memory (memory that cannot be reclaimed under pressure) by container. The OOM Killer is invoked if the working set exceeds the limit. The most detailed level of aggregation is workload.","Byte","CLOUD_APPLICATION","auto|avg|max|min",15
"builtin:kubernetes.workload.pods_desired","Kubernetes: Workload - desired pod count","This metric measures the number of desired pods. The most detailed level of aggregation is workload. The value corresponds to the 'replicas' defined in a deployment resource and to the 'desiredNumberScheduled' for a daemon set resource's status as example.","Count","CLOUD_APPLICATION","auto|avg|max|min",12
"builtin:kubernetes.workload.requests_cpu","Kubernetes: Pod - CPU requests (by workload)","This metric measures the cpu requests. The most detailed level of aggregation is workload. The value is the sum of the cpu requests of all app containers of a pod.","MilliCores","CLOUD_APPLICATION","auto|avg|max|min",15
"builtin:kubernetes.workload.requests_memory","Kubernetes: Pod - memory requests (by workload)","This metric measures the memory requests. The most detailed level of aggregation is workload. The value is the sum of the memory requests of all app containers of a pod.","Byte","CLOUD_APPLICATION","auto|avg|max|min",15
"builtin:kubernetes.containers","Kubernetes: Container count","This metric measures the number of containers. The most detailed level of aggregation is workload. The metric counts the number of all containers.","Count","CLOUD_APPLICATION","auto|avg|max|min",16
"builtin:kubernetes.events","Kubernetes: Event count","This metric counts Kubernetes events. The most detailed level of aggregation is the event reason. The value corresponds to the count of events returned by the Kubernetes events endpoint. This metric depends on Kubernetes event monitoring. It will not show any datapoints for the period in which event monitoring is deactivated.","Count","KUBERNETES_CLUSTER","auto|value",21
"builtin:kubernetes.nodes","Kubernetes: Node count","This metric measures the number of nodes. The most detailed level of aggregation is cluster. The value is the count of all nodes.","Count","KUBERNETES_CLUSTER","auto|avg|max|min",7
"builtin:kubernetes.pods","Kubernetes: Pod count (by workload)","This metric measures the number of pods. The most detailed level of aggregation is workload. The value corresponds to the count of all pods.","Count","CLOUD_APPLICATION","auto|avg|max|min",19
"builtin:kubernetes.workloads","Kubernetes: Workload count","This metric measures the number of workloads. The most detailed level of aggregation is namespace. The value corresponds to the count of all workloads.","Count","CLOUD_APPLICATION_NAMESPACE","auto|avg|max|min",6
"builtin:pgi.availability","Process availability %","This metric provides the percentage of time when a process is available. It is sent once per minute with a 10-second granularity - six samples are aggregated every minute. If the process is available for a whole minute; the value is 100%. A 0% value indicates that it is not running. It has a "Process" dimension (dt.entity.process_group_instance).","Percent","PROCESS_GROUP_INSTANCE","auto|avg",1
"builtin:pgi.availability.state","Process availability","Process availability state metric reported in 1 minute intervals","Count","PROCESS_GROUP_INSTANCE","auto|value",8
"builtin:queue.incoming_requests","Incoming messages","The number of incoming messages on the queue or topic","Count","SERVICE","auto|avg|count|max|min|sum",2
"builtin:queue.outgoing_requests","Outgoing messages","The number of outgoing messages from the queue or topic","Count","SERVICE","auto|avg|count|max|min|sum",2
"builtin:security.vulnerabilities.global.countAffectedProcessGroups.notMuted","Vulnerabilities - affected not-muted process groups count (global)","Total number of unique affected process groups across all open; unmuted vulnerabilities per technology. The metric value is independent of any configured management zone (and thus global).","Count","","auto|avg|max|min",2
"builtin:service.cpu.group.perRequest","CPU time","CPU time consumed by a key request within a particular request type. Request types classify requests; e.g. Resource requests for static assets like CSS or JS files. To learn how Dynatrace calculates service timings; see [Service analysis timings](https://dt-url.net/service-timings).","MicroSecond","SERVICE_METHOD_GROUP","auto|avg|count|max|min|sum",1
"builtin:service.cpu.group.time","Key request CPU time","CPU time consumed by a request type. Request types classify requests; e.g. Resource requests for static assets like CSS or JS files. To learn how Dynatrace calculates service timings; see [Service analysis timings](https://dt-url.net/service-timings).","MicroSecond","SERVICE_METHOD_GROUP","auto|avg|count|max|min|sum",1
"builtin:service.cpu.perRequest","CPU time","CPU time consumed by a particular request. To learn how Dynatrace calculates service timings; see [Service analysis timings](https://dt-url.net/service-timings).","MicroSecond","SERVICE","auto|avg|count|max|min|sum",1
"builtin:service.cpu.time","Service CPU time","CPU time consumed by a particular service. To learn how Dynatrace calculates service timings; see [Service analysis timings](https://dt-url.net/service-timings).","MicroSecond","SERVICE","auto|value",1
"builtin:service.dbconnections.failure","Failed connections","Unsuccessful connection attempts compared to all connection attempts. To learn about database analysis; see [Analyze database services](https://dt-url.net/database-services).","Count","SERVICE","auto|value",1
"builtin:service.dbconnections.failureRate","Connection failure rate","Rate of unsuccessful connection attempts compared to all connection attempts. To learn about database analysis; see [Analyze database services](https://dt-url.net/database-services).","Percent","SERVICE","auto|value",1
"builtin:service.dbconnections.success","Successful connections","Total number of database connections successfully established by this service. To learn about database analysis; see [Analyze database services](https://dt-url.net/database-services).","Count","SERVICE","auto|value",1
"builtin:service.dbconnections.successRate","Connection success rate","Rate of successful connection attempts compared to all connection attempts. To learn about database analysis; see [Analyze database services](https://dt-url.net/database-services).","Percent","SERVICE","auto|value",1
"builtin:service.dbconnections.total","Total number of connections","Total number of database connections that were attempted to be established by this service. To learn about database analysis; see [Analyze database services](https://dt-url.net/database-services).","Count","SERVICE","auto|value",1
"builtin:service.errors.client.count","Number of client side errors","Failed requests for a service measured on client side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE","auto|value",1
"builtin:service.errors.client.rate","Failure rate (client side  errors)","","Percent","SERVICE","auto|avg",1
"builtin:service.errors.client.successCount","Number of calls without  client side errors","","Count","SERVICE","auto|value",1
"builtin:service.errors.fivexx.count","Number of HTTP 5xx errors","HTTP requests with a status code between 500 and 599 for a given key request measured on server side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE","auto|value",1
"builtin:service.errors.fivexx.rate","Failure rate (HTTP 5xx  errors)","","Percent","SERVICE","auto|avg",1
"builtin:service.errors.fivexx.successCount","Number of calls without  HTTP 5xx errors","","Count","SERVICE","auto|value",1
"builtin:service.errors.fourxx.count","Number of HTTP 4xx errors","HTTP requests with a status code between 400 and 499 for a given key request measured on server side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE","auto|value",1
"builtin:service.errors.fourxx.rate","Failure rate (HTTP 4xx  errors)","","Percent","SERVICE","auto|avg",1
"builtin:service.errors.fourxx.successCount","Number of calls without  HTTP 4xx errors","","Count","SERVICE","auto|value",1
"builtin:service.errors.group.client.count","Number of client side errors","Failed requests for a given request type like dynamic web requests or static web requests measured on client side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE_METHOD_GROUP","auto|value",1
"builtin:service.errors.group.client.rate","Failure rate (client side  errors)","","Percent","SERVICE_METHOD_GROUP","auto|avg",1
"builtin:service.errors.group.client.successCount","Number of calls without  client side errors","","Count","SERVICE_METHOD_GROUP","auto|value",1
"builtin:service.errors.group.server.count","Number of server side errors","Failed requests for a given request type like dynamic web requests or static web requests measured on server side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE_METHOD_GROUP","auto|value",1
"builtin:service.errors.group.server.rate","Failure rate (server side  errors)","","Percent","SERVICE_METHOD_GROUP","auto|avg",1
"builtin:service.errors.group.server.successCount","Number of calls without  server side errors","","Count","SERVICE_METHOD_GROUP","auto|value",1
"builtin:service.errors.group.total.count","Number of any errors","Failed requests rate for a given request type like dynamic web requests or static web requests. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE_METHOD_GROUP","auto|value",1
"builtin:service.errors.group.total.rate","Failure rate (any  errors)","","Percent","SERVICE_METHOD_GROUP","auto|avg",1
"builtin:service.errors.group.total.successCount","Number of calls without  any errors","","Count","SERVICE_METHOD_GROUP","auto|value",1
"builtin:service.errors.server.count","Number of server side errors","Failed requests for a service measured on server side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE","auto|value",1
"builtin:service.errors.server.rate","Failure rate (server side  errors)","","Percent","SERVICE","auto|avg",1
"builtin:service.errors.server.successCount","Number of calls without  server side errors","","Count","SERVICE","auto|value",1
"builtin:service.errors.total.count","Number of any errors","Failed requests for a service measured on server side or client side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE","auto|value",1
"builtin:service.errors.total.rate","Failure rate (any  errors)","","Percent","SERVICE","auto|avg",1
"builtin:service.errors.total.successCount","Number of calls without  any errors","","Count","SERVICE","auto|value",1
"builtin:service.keyRequest.count.client","Request count - client","Number of requests for a given key request - measured on the client side. This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.count.server","Request count - server","Number of requests for a given key request - measured on the server side. This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.count.total","Request count","Number of requests for a given key request. This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.cpu.perRequest","CPU per request","CPU time for a given key request.  This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","MicroSecond","SERVICE_METHOD","auto|avg|count|max|min|sum",1
"builtin:service.keyRequest.cpu.time","Service key request CPU time","CPU time for a given key request.  This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","MicroSecond","SERVICE_METHOD","auto|avg|count|max|min|sum",1
"builtin:service.keyRequest.errors.client.count","Number of client side errors","Failed requests for a given key request measured on client side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.errors.client.rate","Failure rate (client side  errors)","","Percent","SERVICE_METHOD","auto|avg",1
"builtin:service.keyRequest.errors.client.successCount","Number of calls without  client side errors","","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.errors.fivexx.count","Number of HTTP 5xx errors","Rate of HTTP requests with a status code between 500 and 599 of a given key request. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.errors.fivexx.rate","Failure rate (HTTP 5xx  errors)","","Percent","SERVICE_METHOD","auto|avg",1
"builtin:service.keyRequest.errors.fivexx.successCount","Number of calls without  HTTP 5xx errors","","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.errors.fourxx.count","Number of HTTP 4xx errors","Rate of HTTP requests with a status code between 400 and 499 of a given key request. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.errors.fourxx.rate","Failure rate (HTTP 4xx  errors)","","Percent","SERVICE_METHOD","auto|avg",1
"builtin:service.keyRequest.errors.fourxx.successCount","Number of calls without  HTTP 4xx errors","","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.errors.server.count","Number of server side errors","Failed requests for a given key request measured on server side. To learn about failure detection; see [Configure service failure detection](https://dt-url.net/service-failuredetection).","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.errors.server.rate","Failure rate (server side  errors)","","Percent","SERVICE_METHOD","auto|avg",1
"builtin:service.keyRequest.errors.server.successCount","Number of calls without  server side errors","","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.response.client","Client side response time","Response time for a given key request - measured on the client side.  This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","MicroSecond","SERVICE_METHOD","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.keyRequest.response.server","Server side response time","Response time for a given key request - measured on the server side.  This metric is written for each request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","MicroSecond","SERVICE_METHOD","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.keyRequest.response.time","Key request response time","Response time for a given key request.  This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","MicroSecond","SERVICE_METHOD","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.keyRequest.successes.server.rate","Success rate (server side)","","Percent","SERVICE_METHOD","auto|avg",1
"builtin:service.keyRequest.dbChildCallCount","Number of calls to databases","","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.dbChildCallTime","Time spent in database calls","","MicroSecond","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.ioTime","IO time","","MicroSecond","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.lockTime","Lock time","","MicroSecond","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.nonDbChildCallCount","Number of calls to other services","","Count","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.nonDbChildCallTime","Time spent in calls to other services","","MicroSecond","SERVICE_METHOD","auto|value",1
"builtin:service.keyRequest.totalProcessingTime","Total processing time","Total processing time for a given key request. This time includes potential further asynchronous processing. This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","MicroSecond","SERVICE_METHOD","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.keyRequest.waitTime","Wait time","","MicroSecond","SERVICE_METHOD","auto|value",1
"builtin:service.requestCount.client","Request count - client","Number of requests received by a given service - measured on the client side. This metric allows service splittings. To learn how Dynatrace detects and analyzes services; see [Services](https://dt-url.net/am-services).","Count","SERVICE","auto|value",1
"builtin:service.requestCount.server","Request count - server","Number of requests received by a given service - measured on the server side. This metric allows service splittings. To learn how Dynatrace detects and analyzes services; see [Services](https://dt-url.net/am-services).","Count","SERVICE","auto|value",1
"builtin:service.requestCount.total","Request count","Number of requests received by a given service. This metric allows service splittings. To learn how Dynatrace detects and analyzes services; see [Services](https://dt-url.net/am-services).","Count","SERVICE","auto|value",1
"builtin:service.response.group.client","Client side response time","Response time for a given key request per request type - measured on the client side.  This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","MicroSecond","SERVICE_METHOD_GROUP","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.response.group.server","Server side response time","Response time for a given key request per request type - measured on the server side. This metric is written for each key request. To learn more about key requests; see [Monitor key request](https://dt-url.net/key-request).","MicroSecond","SERVICE_METHOD_GROUP","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.response.client","Client side response time","","MicroSecond","SERVICE","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.response.server","Server side response time","","MicroSecond","SERVICE","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.response.time","Response time","Time consumed by a particular service until a response is sent back to the calling application; process; service etc.To learn how Dynatrace calculates service timings; see [Service analysis timings](https://dt-url.net/service-timings).","MicroSecond","SERVICE","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.successes.server.rate","Success rate (server side)","","Percent","SERVICE","auto|avg",1
"builtin:service.totalProcessingTime","Total processing time","Total time consumed by a particular service including asynchronous processing. Time includes the factor that asynchronous processing can still occur after responses are sent.To learn how Dynatrace calculates service timings; see [Service analysis timings](https://dt-url.net/service-timings).","MicroSecond","SERVICE","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.totalProcessingTime.group.totalProcessingTime","Total processing time","Total time consumed by a particular request type including asynchronous processing. Time includes the factor that asynchronous processing can still occur after responses are sent. To learn how Dynatrace calculates service timings; see [Service analysis timings](https://dt-url.net/service-timings).","MicroSecond","SERVICE_METHOD_GROUP","auto|avg|count|max|median|min|percentile|sum",1
"builtin:service.dbChildCallCount","Number of calls to databases","","Count","SERVICE","auto|value",1
"builtin:service.dbChildCallTime","Time spent in database calls","","MicroSecond","SERVICE","auto|value",1
"builtin:service.ioTime","IO time","","MicroSecond","SERVICE","auto|value",1
"builtin:service.lockTime","Lock time","","MicroSecond","SERVICE","auto|value",1
"builtin:service.nonDbChildCallCount","Number of calls to other services","","Count","SERVICE","auto|value",1
"builtin:service.nonDbChildCallTime","Time spent in calls to other services","","MicroSecond","SERVICE","auto|value",1
"builtin:service.waitTime","Wait time","","MicroSecond","SERVICE","auto|value",1
"builtin:synthetic.browser.availability.location.total","Availability rate (by location) [browser monitor]","The availability rate of browser monitors.","Percent","SYNTHETIC_TEST","auto|avg",2
"builtin:synthetic.browser.availability.location.totalWoMaintenanceWindow","Availability rate - excl. maintenance windows (by location) [browser monitor]","The availability rate of browser monitors excluding maintenance windows.","Percent","SYNTHETIC_TEST","auto|avg",2
"builtin:synthetic.external.availability.location.total","Availability rate (by location) [third-party monitor]","The availability rate of third-party monitors.","Percent","EXTERNAL_SYNTHETIC_TEST","auto|avg",2
"builtin:synthetic.external.availability.location.totalWoMaintenanceWindow","Availability rate - excl. maintenance windows (by location) [third-party monitor]","The availability rate of third-party monitors excluding maintenance windows.","Percent","EXTERNAL_SYNTHETIC_TEST","auto|avg",2
"builtin:synthetic.http.availability.location.total","Availability rate (by location) [HTTP monitor]","The availability rate of HTTP monitors.","Percent","HTTP_CHECK","auto|avg",2
"builtin:synthetic.http.availability.location.totalWoMaintenanceWindow","Availability rate - excl. maintenance windows (by location) [HTTP monitor]","The availability rate of HTTP monitors excluding maintenance windows.","Percent","HTTP_CHECK","auto|avg",2
"builtin:tech.generic.cpu.groupSuspensionTime","Process group total CPU time during GC suspensions","This metric provides statistics about CPU usage for process groups of garbage-collected technologies. The metric value is the sum of CPU time used during garbage collector suspensions for every process (including its workers) in a process group. It has a "Process Group" dimension.","MicroSecond","PROCESS_GROUP","auto|value",1
"builtin:tech.generic.cpu.groupTotalTime","Process group total CPU time","This metric provides the total CPU time used by a process group. The metric value is the sum of CPU time every process (including its workers) of the process group uses. The result is expressed in microseconds. It can help to identify the most CPU-intensive technologies in the monitored environment. It has a "Process Group" dimension.","MicroSecond","PROCESS_GROUP","auto|value",1
"builtin:tech.generic.cpu.suspensionTime","Process total CPU time during GC suspensions","This metric provides statistics about CPU usage for garbage-collected processes. The metric value is the sum of CPU time used during garbage collector suspensions for all process workers. It has a "Process" dimension (dt.entity.process_group_instance).","MicroSecond","PROCESS_GROUP_INSTANCE","auto|value",1
"builtin:tech.generic.cpu.totalTime","Process total CPU time","This metric provides the CPU time used by a process. The metric value is the sum of CPU time every process worker uses. The result is expressed in microseconds. It has a "Process" dimension (dt.entity.process_group_instance).","MicroSecond","PROCESS_GROUP_INSTANCE","auto|value",1
"builtin:tech.generic.cpu.usage","Process CPU usage","This metric is the percentage of the CPU usage of a process. The metric value is the sum of CPU time every process worker uses divided by the total available CPU time. The result is expressed in percentage. A value of 100% indicates that the process uses all available CPU resources of the host.","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.handles.fileDescriptorsMax","Process file descriptors max","This metric provides statistics about the file descriptor resource limits. It is supported on Linux. The metric value is the total limit of file descriptors that all process workers can open. It is sent once per minute with a 10-second granularity - (six samples are aggregated every minute).","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.handles.fileDescriptorsPercentUsed","Process file descriptors used per PID","This metric provides the file descriptor usage statistics. It is supported on Linux. The metric value is the highest percentage of the currently used file descriptor limit among process workers. It is sent once per minute with a 10-second granularity - (six samples are aggregated every minute). It offers two dimensions: "Process" (`dt.entity.process_group_instance`) and pid dimension corresponding to the PID with the highest percentage of available descriptors usage.","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.generic.handles.fileDescriptorsUsed","Process file descriptors used","This metric provides statistics about file descriptor usage. It is supported on Linux. The metric value is the total number of file descriptors all process workers have opened. You can use it to detect processes that may cause the system to reach the limit of open file descriptors.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.io.bytesRead","Process I/O read bytes","This metric provides statistics about the I/O read operations of a process. The metric value is a sum of I/O bytes read from the storage layer by all process workers per second. High values help to identify bottlenecks reducing process performance caused by the slow read speed of the storage device.","BytePerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.io.bytesTotal","Process I/O bytes total","This metric provides statistics about I/O operations for a process. The metric value is a sum of I/O bytes read and written by all process workers per second.","BytePerSecond","PROCESS_GROUP_INSTANCE","auto|value",1
"builtin:tech.generic.io.bytesWritten","Process I/O write bytes","This metric provides statistics about the I/O write operations of a process. The metric value is a sum of I/O bytes written to the storage layer by all process workers per second. High values help to identify bottlenecks reducing process performance caused by the slow write speed of the storage device.","BytePerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.io.reqBytesRead","Process I/O requested read bytes","This metric provides statistics about the I/O read operations a process requests. It is supported only on Linux and AIX. The metric value is a sum of I/O bytes requested to be read from the storage by worker processes per second. It includes additional read operations; such as terminal I/O. It does not indicate the actual disk I/O operations; as some parts of the read operation might have been satisfied from the page cache.","BytePerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.io.reqBytesWrite","Process I/O requested write bytes","This metric provides statistics about the I/O write operations a process requests. It is supported on Linux and AIX. The metric value is a sum of I/O bytes requested to be written to the storage by PGI processes per second. It includes additional write operations; such as terminal I/O. It does not indicate the actual disk I/O operations; as some parts of the write operation might have been satisfied from the page cache.","BytePerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.mem.pageFaults","Process page faults counter","This metric is the rate of page faults for a process. The metric value is the sum of page faults per time unit of every process worker. A page fault occurs when the process attempts to access a memory block not stored in the RAM; which means that the block has to be identified in the virtual memory and then loaded from the storage. Lower values are better. A high number of page faults may indicate reduced performance due to insufficient memory size.","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.mem.usage","Process memory usage","This metric is the percentage of memory used by a process. It helps to identify processes with high memory resource consumption and memory leaks. The metric value is the sum of the memory used by every process worker divided by the total available memory in the host.","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.mem.workingSetSize","Process memory","This metric is the memory usage of a process. It helps to identify processes with high memory resource consumption and memory leaks. The metric value represents the sum of every process worker's used memory size (including shared memory).","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.baseReRx","Retransmission base received","Number of retransmitted packets base received per second on host","PerSecond","HOST","auto|avg|max|min",2
"builtin:tech.generic.network.packets.baseReRxAggr","Retransmission base received","Number of retransmitted packets base received per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.baseReTx","Retransmission base sent","Number of retransmitted packets base sent per second on host","PerSecond","HOST","auto|avg|max|min",2
"builtin:tech.generic.network.packets.baseReTxAggr","Retransmission base sent","Number of retransmitted packets base sent per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.reRx","Retransmitted packets received","Number of retransmitted packets received per second on host","PerSecond","HOST","auto|avg|max|min",2
"builtin:tech.generic.network.packets.reRxAggr","Retransmitted packets received","Number of retransmitted packets received per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.reTx","Retransmitted packets sent","Number of retransmitted packets base sent per second on host","PerSecond","HOST","auto|avg|max|min",2
"builtin:tech.generic.network.packets.reTxAggr","Retransmitted packets","Number of retransmitted packets sent per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.retransmission","Packet retransmissions","Packet retransmissions percent","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.retransmissionIn","Incoming packet retransmissions","Incoming packet retransmissions percent","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.retransmissionOut","Outgoing packet retransmissions","Outgoing packet retransmissions percent","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.rx","Packets received","Number of packets received per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.packets.tx","Packets sent","Number of packets sent per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.sessions.connectivity","TCP connectivity","Percentage of successfully established TCP sessions","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.sessions.new","New session received","Number of new incoming TCP sessions per second","PerSecond","HOST","auto|avg|max|min",2
"builtin:tech.generic.network.sessions.newAggr","New session received","Number of new sessions received per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.sessions.newLocal","New session received","Number of new sessions received per second on localhost","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.sessions.reset","Session reset received","Number of incoming TCP sessions with reset error per second","PerSecond","HOST","auto|avg|max|min",2
"builtin:tech.generic.network.sessions.resetAggr","Session reset received","Number of session resets received per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.sessions.resetLocal","Session reset received","Number of session resets received per second on localhost","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.sessions.timeout","Session timeout received","Number of incoming TCP sessions with timeout error per second","PerSecond","HOST","auto|avg|max|min",2
"builtin:tech.generic.network.sessions.timeoutAggr","Session timeout received","Number of session timeouts received per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.sessions.timeoutLocal","Session timeout received","Number of session timeouts received per second on localhost","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.traffic.traffic","Traffic","Summary of incoming and outgoing network traffic","BitPerSecond","PROCESS_GROUP_INSTANCE","auto|value",1
"builtin:tech.generic.network.traffic.trafficIn","Traffic in","Incoming network traffic at PGI","BitPerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.traffic.trafficOut","Traffic out","Outgoing network traffic from PGI","BitPerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.bytesRx","Bytes received","Number of bytes received per second","BytePerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.bytesTx","Bytes sent","Number of bytes sent per second","BytePerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.latency","Ack-round-trip time","Average latency between outgoing TCP data and ACK","MilliSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.load","Requests","Number of requests per second","PerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.responsiveness","Server responsiveness","Server responsiveness in microseconds","MicroSecond","PROCESS_GROUP_INSTANCE","auto|avg|count|max|median|min|percentile|sum",1
"builtin:tech.generic.network.roundTrip","Round-trip time","Average TCP session handshake RTT","MilliSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.network.throughput","Throughput","Used network bandwidth","BytePerSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.generic.count","Process count per process group","This metric provides the number of processes in a process group. It can tell how many instances of the technology are running in the monitored environment. It has a "Process Group" dimension.","Count","PROCESS_GROUP","auto|value",1
"builtin:tech.generic.processCount","Worker processes","This metric is the number of process workers. Too few worker processes may lead to performance degradation; while too many may waste available resources. Configuration of workers should be suitable for the average workload and be able to scale up with higher demand.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.go.http.badGateways","Go: 502 responses","The number of responses that indicate invalid service responses produced by an application.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.go.http.latency","Go: Response latency","The average response time from the application to clients.","MilliSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.http.responses5xx","Go: 5xx responses","The number of responses that indicate repeatedly crashing apps or response issues from applications.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.go.http.totalRequests","Go: Total requests","The number of all requests representing the overall traffic flow.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.go.memory.heap.idle","Go: Heap idle size","The amount of memory not assigned to the heap or stack. Idle memory can be returned to the operating system or retained by the Go runtime for later reassignment to the heap or stack.","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.memory.heap.live","Go: Heap live size","The amount of memory considered live by the Go garbage collector. This metric accumulates memory retained by the most recent garbage collector run and allocated since then.","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.memory.heap.objCount","Go: Heap allocated Go objects count","The number of Go objects allocated on the Go heap.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.memory.pool.committed","Go: Committed memory","The amount of memory committed to the Go runtime heap.","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",3
"builtin:tech.go.memory.pool.used","Go: Used memory","The amount of memory used by the Go runtime heap.","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",3
"builtin:tech.go.memory.gcCount","Go: Garbage collector invocation count","The number of Go garbage collector runs.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.go.native.cgoCalls","Go: Go to C language (cgo) call count","The number of Go to C language (cgo) calls.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.go.native.sysCalls","Go: Go runtime system call count","The number of system calls executed by the Go runtime. This number doesn't include system calls performed by user code.","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.go.scheduling.g.runningCount","Go: Application Goroutine count","The number of Goroutines instantiated by the user application.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.scheduling.g.systemCount","Go: System Goroutine count","The number of Goroutines instantiated by the Go runtime.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.scheduling.m.count","Go: Worker thread count","The number of operating system threads instantiated to execute Goroutines. Go doesn't terminate worker threads; it keeps them in a parked state for future reuse.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.scheduling.m.idlingCount","Go: Parked worker thread count","The number of worker threads parked by Go runtime. A parked worker thread doesn't consume CPU cycles until the Go runtime unparks the thread.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.scheduling.m.spinningCount","Go: Out-of-work worker thread count","The number of worker threads whose associated scheduling context has no more Goroutines to execute. When this happens; the worker thread attempts to steal Goroutines from another scheduling context or the global run queue. If the stealing fails; the worker thread parks itself after some time. This same mechanism applies to a high workload scenario. When an idle scheduling context exists; the Go runtime unparks a parked worker thread and associates it with the idle scheduling context. The unparked worker thread is now in the 'out of work' state and starts Goroutine stealing.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.scheduling.p.idleCount","Go: Idle scheduling context count","The number of scheduling contexts that have no more Goroutines to execute and for which Goroutine acquisition from the global run queue or other scheduling contexts has failed.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.go.scheduling.globalQSize","Go: Global Goroutine run queue size","The number of Goroutines in the global run queue. Goroutines are placed in the global run queue if the worker thread used to execute a blocking system call can't acquire a scheduling context. Scheduling contexts periodically acquire Goroutines from the global run queue.","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.jvm.classes.loaded","JVM loaded classes","The number of classes that are currently loaded in the Java virtual machine; https://dt-url.net/l2c34jw","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.jvm.classes.total","JVM total number of loaded classes","The total number of classes that have been loaded since the Java virtual machine has started execution; https://dt-url.net/d0y347x","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.jvm.classes.unloaded","JVM unloaded classes","The total number of classes unloaded since the Java virtual machine has started execution; https://dt-url.net/d7g34bi","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.jvm.memory.gc.activationCount","Garbage collection total activation count","The total number of collections that have occurred for all pools; https://dt-url.net/oz834vd","Count","PROCESS_GROUP_INSTANCE","auto|value",1
"builtin:tech.jvm.memory.gc.collectionTime","Garbage collection total collection time","The approximate accumulated collection elapsed time in milliseconds for all pools; https://dt-url.net/oz834vd","MilliSecond","PROCESS_GROUP_INSTANCE","auto|value",1
"builtin:tech.jvm.memory.gc.suspensionTime","Garbage collection suspension time","Time spent in milliseconds between GC pause starts and GC pause ends; https://dt-url.net/zj434js","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.jvm.memory.pool.collectionCount","Garbage collection count","The total number of collections that have occurred in that pool; https://dt-url.net/z9034yg","Count","PROCESS_GROUP_INSTANCE","auto|value",4
"builtin:tech.jvm.memory.pool.collectionTime","Garbage collection time","The approximate accumulated collection elapsed time in milliseconds in that pool; https://dt-url.net/z9034yg","MilliSecond","PROCESS_GROUP_INSTANCE","auto|value",4
"builtin:tech.jvm.memory.pool.committed","JVM heap memory pool committed bytes","The amount of memory (in bytes) that is guaranteed to be available for use by the Java virtual machine; https://dt-url.net/1j034o0","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",3
"builtin:tech.jvm.memory.pool.max","JVM heap memory max bytes","The maximum amount of memory (in bytes) that can be used for memory management; https://dt-url.net/1j034o0","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",3
"builtin:tech.jvm.memory.pool.used","JVM heap memory pool used bytes","The amount of memory currently used by the memory pool (in bytes); https://dt-url.net/1j034o0","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",3
"builtin:tech.jvm.memory.runtime.free","JVM runtime free memory","An approximation to the total amount of memory currently available for future allocated objects; measured in bytes; https://dt-url.net/2mm34yx","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.jvm.memory.runtime.max","JVM runtime max memory","The maximum amount of memory that the virtual machine will attempt to use; measured in bytes; https://dt-url.net/lzq34mm","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.jvm.memory.runtime.total","JVM runtime total memory","The total amount of memory currently available for current and future objects; measured in bytes; https://dt-url.net/otu34eo","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.jvm.memory.memAllocationBytes","Process memory allocation bytes","","Byte","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.jvm.memory.memAllocationCount","Process memory allocation objects count","","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.jvm.memory.memSurvivorsBytes","Process memory survived objects bytes","","Byte","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.jvm.memory.memSurvivorsCount","Process memory survived objects count","","Count","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.jvm.threads.avgActiveThreadCount","JVM average number of active threads","","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",3
"builtin:tech.jvm.threads.avgInactiveThreadCount","JVM average number of inactive threads","","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.jvm.threads.count","JVM thread count","The current number of live threads including both daemon and non-daemon threads; https://dt-url.net/s02346y","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.jvm.threads.totalCpuTime","JVM total CPU time","","MilliSecond","PROCESS_GROUP_INSTANCE","auto|value",2
"builtin:tech.nodejs.uvLoop.activeHandles","Node.js: Active handles","Average number of active handles in the event loop","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.nodejs.uvLoop.count","Node.js: Event loop tick frequency","Average number of event loop iterations (per 10 seconds interval)","Count","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.nodejs.uvLoop.loopLatency","Node.js: Event loop latency","Average latency of expected event completion","NanoSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.nodejs.uvLoop.processedLatency","Node.js: Work processed latency","Average latency of a work item being enqueued and callback being called","NanoSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.nodejs.uvLoop.totalTime","Node.js: Event loop tick duration","Average duration of an event loop iteration (tick)","NanoSecond","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.nodejs.uvLoop.utilization","Node.js: Event loop utilization","Event loop utilization represents the percentage of time the event loop has been active","Percent","PROCESS_GROUP_INSTANCE","auto|avg|max|min",2
"builtin:tech.nodejs.v8heap.gcHeapUsed","Node.js: GC heap used","Total size of allocated V8 heap used by application data (post-GC memory snapshot)","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.nodejs.v8heap.rss","Node.js: Process Resident Set Size (RSS)","Amount of space occupied in the main memory","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
"builtin:tech.nodejs.v8heap.total","Node.js: V8 heap total","Total size of allocated V8 heap","Byte","PROCESS_GROUP_INSTANCE","auto|avg|max|min",1
