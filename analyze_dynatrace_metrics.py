#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze the collected Dynatrace metrics and provide comprehensive statistics
"""

import json
import glob
from collections import defaultdict, Counter
from datetime import datetime

def load_metrics_files():
    """Load all Dynatrace metrics JSON files"""
    metrics_files = glob.glob("dynatrace*metrics*.json")
    all_metrics = {}
    
    for file_path in metrics_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                all_metrics[file_path] = data
                print(f"Loaded {data.get('total_metrics', len(data.get('metrics', [])))} metrics from {file_path}")
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
    
    return all_metrics

def analyze_metrics(all_metrics):
    """Analyze the collected metrics and provide statistics"""
    
    print("\n" + "="*80)
    print("DYNATRACE METRICS ANALYSIS")
    print("="*80)
    
    # Combine all unique metrics
    unique_metrics = {}
    entity_types = Counter()
    units = Counter()
    aggregation_types = Counter()
    metric_categories = Counter()
    
    for file_path, data in all_metrics.items():
        metrics = data.get('metrics', [])
        
        for metric in metrics:
            metric_id = metric.get('metricId', '')
            
            # Store unique metrics
            if metric_id not in unique_metrics:
                unique_metrics[metric_id] = metric
            
            # Count entity types
            for entity_type in metric.get('entityType', []):
                entity_types[entity_type] += 1
            
            # Count units
            unit = metric.get('unit', 'Unknown')
            units[unit] += 1
            
            # Count aggregation types
            for agg_type in metric.get('aggregationTypes', []):
                aggregation_types[agg_type] += 1
            
            # Categorize metrics by prefix
            if metric_id:
                category = metric_id.split(':')[0] if ':' in metric_id else 'other'
                metric_categories[category] += 1
    
    print(f"\nTOTAL UNIQUE METRICS: {len(unique_metrics)}")
    print(f"FILES PROCESSED: {len(all_metrics)}")
    
    print(f"\nMETRIC CATEGORIES (Top 10):")
    for category, count in metric_categories.most_common(10):
        print(f"  {category}: {count}")
    
    print(f"\nENTITY TYPES (Top 10):")
    for entity_type, count in entity_types.most_common(10):
        print(f"  {entity_type}: {count}")
    
    print(f"\nUNITS (Top 10):")
    for unit, count in units.most_common(10):
        print(f"  {unit}: {count}")
    
    print(f"\nAGGREGATION TYPES:")
    for agg_type, count in aggregation_types.most_common():
        print(f"  {agg_type}: {count}")
    
    return unique_metrics

def export_comprehensive_metrics(unique_metrics):
    """Export all unique metrics to a comprehensive file"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"dynatrace_comprehensive_metrics_{timestamp}.json"
    
    # Create comprehensive structure
    comprehensive_data = {
        'timestamp': datetime.now().isoformat(),
        'total_unique_metrics': len(unique_metrics),
        'metrics': list(unique_metrics.values())
    }
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_data, f, indent=2, ensure_ascii=False)
        
        print(f"\nCOMPREHENSIVE METRICS EXPORTED TO: {output_file}")
        return output_file
    except Exception as e:
        print(f"Error exporting comprehensive metrics: {e}")
        return None

def create_metrics_summary(unique_metrics):
    """Create a summary CSV file with key metric information"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_file = f"dynatrace_metrics_summary_{timestamp}.csv"
    
    try:
        with open(csv_file, 'w', encoding='utf-8') as f:
            # Write header
            f.write("MetricID,DisplayName,Description,Unit,EntityTypes,AggregationTypes,DimensionCount\n")
            
            # Write metrics data
            for metric in unique_metrics.values():
                metric_id = metric.get('metricId', '').replace(',', ';')
                display_name = metric.get('displayName', '').replace(',', ';')
                description = metric.get('description', '').replace(',', ';').replace('\n', ' ')
                unit = metric.get('unit', '')
                entity_types = '|'.join(metric.get('entityType', []))
                agg_types = '|'.join(metric.get('aggregationTypes', []))
                dimension_count = len(metric.get('dimensionDefinitions', []))
                
                f.write(f'"{metric_id}","{display_name}","{description}","{unit}","{entity_types}","{agg_types}",{dimension_count}\n')
        
        print(f"METRICS SUMMARY CSV EXPORTED TO: {csv_file}")
        return csv_file
    except Exception as e:
        print(f"Error creating CSV summary: {e}")
        return None

def main():
    """Main analysis function"""
    print("Dynatrace Metrics Analyzer")
    print("=" * 50)
    
    # Load all metrics files
    all_metrics = load_metrics_files()
    
    if not all_metrics:
        print("No metrics files found!")
        return
    
    # Analyze metrics
    unique_metrics = analyze_metrics(all_metrics)
    
    # Export comprehensive file
    comprehensive_file = export_comprehensive_metrics(unique_metrics)
    
    # Create CSV summary
    csv_file = create_metrics_summary(unique_metrics)
    
    print(f"\n" + "="*80)
    print("ANALYSIS COMPLETE")
    print("="*80)
    print(f"Files created:")
    if comprehensive_file:
        print(f"  - {comprehensive_file} (Complete JSON)")
    if csv_file:
        print(f"  - {csv_file} (CSV Summary)")

if __name__ == "__main__":
    main()
