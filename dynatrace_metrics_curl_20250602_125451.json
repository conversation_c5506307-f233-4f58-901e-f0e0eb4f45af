{"totalCount": 25330, "nextPageKey": "__-rq96t3q0GCQABAgMHBAUGDAEAIGJ1aWx0aW46dGVjaC5ub2RlanMudjhoZWFwLnRvdGFsAAAAAAAAAfQAAQABKgCLuOL88jIAAAAAAAPQkAAAAfMEAAABly-Z36cEAAABlzAHvKcBAwD__6ur3q3erQ", "metrics": [{"metricId": "builtin:apps.mobile.sessionCount", "displayName": "Session count (by OS, app version, crash replay feature status) [mobile]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "App Version", "name": null, "displayName": "App Version", "index": null, "type": "STRING"}, {"key": "Crash replay feature status", "name": null, "displayName": "Crash replay feature status", "index": null, "type": "STRING"}, {"key": "dt.entity.mobile_application", "name": null, "displayName": "Mobile App", "index": null, "type": "ENTITY"}, {"key": "dt.entity.os", "name": null, "displayName": "OS", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.apdex.osAndGeo", "displayName": "Apdex (by OS, geolocation) [mobile, custom]", "description": "The Apdex rating for all captured user actions.", "unit": "Unspecified", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 2, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.apdex.osAndVersion", "displayName": "Apdex (by OS, app version) [mobile, custom]", "description": "The Apdex rating for all captured user actions.", "unit": "Unspecified", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.crashAffectedUsers.os", "displayName": "User count - estimated users affected by crashes (by OS) [mobile, custom]", "description": "The estimated number of unique users affected by a crash. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.crashAffectedUsers.osAndVersion-std", "displayName": "User count - estimated users affected by crashes (by OS, app version) [mobile, custom]", "description": "The estimated number of unique users affected by a crash. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.crashAffectedUsersRate.os", "displayName": "User rate - estimated users affected by crashes (by OS) [mobile, custom]", "description": "The estimated percentage of unique users affected by a crash. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Percent", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.crashCount.osAndGeo", "displayName": "Crash count (by OS, geolocation) [mobile, custom]", "description": "The number of detected crashes.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 2, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.crashCount.osAndVersion-std", "displayName": "Crash count (by OS, app version) [mobile, custom]", "description": "The number of detected crashes.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.crashFreeUsersRate.os", "displayName": "User rate - estimated crash free users (by OS) [mobile, custom]", "description": "The estimated percentage of unique users not affected by a crash. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Percent", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.newUsers.os", "displayName": "New user count (by OS) [mobile, custom]", "description": "The number of users that launched the application(s) for the first time. The metric is tied to specific devices, so users are counted multiple times if they install the application on multiple devices. The metric doesn't distinguish between multiple users that share the same device and application installation.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.requestCount.osAndProvider", "displayName": "Request count (by OS, provider) [mobile, custom]", "description": "The number of captured web requests.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Provider Type", "name": "Provider Type", "displayName": "Provider Type", "index": 2, "type": "STRING"}, {"key": "Provider", "name": "Provider", "displayName": "Provider", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestCount.osAndVersion", "displayName": "Request count (by OS, app version) [mobile, custom]", "description": "The number of captured web requests.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestErrorCount.osAndProvider", "displayName": "Request error count (by OS, provider) [mobile, custom]", "description": "The number of detected web request errors.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Provider Type", "name": "Provider Type", "displayName": "Provider Type", "index": 2, "type": "STRING"}, {"key": "Provider", "name": "Provider", "displayName": "Provider", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestErrorCount.osAndVersion", "displayName": "Request error count (by OS, app version) [mobile, custom]", "description": "The number of detected web request errors.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestErrorRate.osAndProvider", "displayName": "Request error rate (by OS, provider) [mobile, custom]", "description": "The percentage of web requests with detected errors.", "unit": "Percent", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Provider Type", "name": "Provider Type", "displayName": "Provider Type", "index": 2, "type": "STRING"}, {"key": "Provider", "name": "Provider", "displayName": "Provider", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestErrorRate.osAndVersion", "displayName": "Request error rate (by OS, app version) [mobile, custom]", "description": "The percentage of web requests with detected errors.", "unit": "Percent", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestTimes.osAndProvider", "displayName": "Request duration (by OS, provider) [mobile, custom]", "description": "The duration of web requests.", "unit": "MilliSecond", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Provider Type", "name": "Provider Type", "displayName": "Provider Type", "index": 2, "type": "STRING"}, {"key": "Provider", "name": "Provider", "displayName": "Provider", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestTimes.osAndVersion", "displayName": "Request duration (by OS, app version) [mobile, custom]", "description": "The duration of web requests.", "unit": "MilliSecond", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.agentVersionAndOs", "displayName": "Session count (by agent version, OS) [mobile, custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "Agent Version", "name": "Agent Version", "displayName": "Agent Version", "index": 1, "type": "STRING"}, {"key": "Operating System", "name": "Operating System", "displayName": "Operating System", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.osAndCrashReportingLevel", "displayName": "Session count (by OS, crash reporting level) [mobile, custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Crash reporting level", "name": "Crash reporting level", "displayName": "Crash reporting level", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.osAndDataCollectionLevel", "displayName": "Session count (by OS, data collection level) [mobile, custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Data collection level", "name": "Data collection level", "displayName": "Data collection level", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.osAndGeo", "displayName": "Session count - estimated (by OS, geolocation) [mobile, custom]", "description": "The estimated number of captured user sessions. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 2, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.osAndVersion-std", "displayName": "Session count (by OS, app version) [mobile, custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.uaCount.geoAndApdex", "displayName": "Action count (by geolocation, Apdex category) [mobile, custom]", "description": "The number of captured user actions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "Apdex category", "name": "Apdex category", "displayName": "Apdex category", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.uaCount.osAndApdex", "displayName": "Action count (by OS, Apdex category) [mobile, custom]", "description": "The number of captured user actions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Apdex category", "name": "Apdex category", "displayName": "Apdex category", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.uaCount.osAndVersion", "displayName": "Action count (by OS, app version) [mobile, custom]", "description": "The number of captured user actions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.uaDuration.osAndVersion", "displayName": "Action duration (by OS, app version) [mobile, custom]", "description": "The duration of user actions.", "unit": "MicroSecond", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.userCount.osAndGeo", "displayName": "User count - estimated (by OS, geolocation) [mobile, custom]", "description": "The estimated number of unique users that have a mapped geolocation. The metric is based on 'internalUserId'. When 'dataCollectionLevel' is set to 'performance' or 'off', 'internalUserId' is changed at each app start. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 2, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.userCount.osAndVersion-std", "displayName": "User count - estimated (by OS, app version) [mobile, custom]", "description": "The estimated number of unique users. The metric is based on 'internalUserId'. When 'dataCollectionLevel' is set to 'performance' or 'off', 'internalUserId' is changed at each app start. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.load.browser", "displayName": "Action count - load action (by browser) [web]", "description": "The number of load actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.xhr.browser", "displayName": "Action count - XHR action (by browser) [web]", "description": "The number of XHR actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.category", "displayName": "Action count (by Apdex category) [web]", "description": "The number of user actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Apdex category", "name": "Apdex category", "displayName": "Apdex category", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.summary", "displayName": "Action with key performance metric count (by action type, geolocation, user type) [web]", "description": "The number of user actions that have a key performance metric and mapped geolocation.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}, {"key": "Action type", "name": "Action type", "displayName": "Action type", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.actionDuration.load.browser", "displayName": "Action duration - load action (by browser) [web]", "description": "The duration of load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionDuration.xhr.browser", "displayName": "Action duration - XHR action (by browser) [web]", "description": "The duration of XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionsPerSession", "displayName": "Actions per session average (by users, user type) [web]", "description": "The average number of user actions per user session.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.activeSessions", "displayName": "Session count - estimated active sessions (by users, user type) [web]", "description": "The estimated number of active user sessions. An active session is one in which a user has been confirmed to still be active at a given time. For this high-cardinality metric, the HyperLogLog algorithm is used to approximate the session count.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.activeUsersEst", "displayName": "User count - estimated active users (by users, user type) [web]", "description": "The estimated number of unique active users. For this high-cardinality metric, the HyperLogLog algorithm is used to approximate the user count.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.affectedUas", "displayName": "User action rate - affected by JavaScript errors (by user type) [web]", "description": "The percentage of user actions with detected JavaScript errors.", "unit": "Percent", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.apdex.userType", "displayName": "Apdex (by user type) [web]", "description": "", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.apdex.userType.geoBig", "displayName": "Apdex (by geolocation, user type) [web]", "description": "The average Apdex rating for user actions that have a mapped geolocation.", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.bouncedSessionRatio", "displayName": "Bounce rate (by users, user type) [web]", "description": "The percentage of sessions in which users viewed only a single page and triggered only a single web request. Calculated by dividing single-page sessions by all sessions.", "unit": "Percent", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.cumulativeLayoutShift.load.userType", "displayName": "Cumulative Layout Shift - load action (by user type) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.cumulativeLayoutShift.load.userType.geo", "displayName": "Cumulative Layout Shift - load action (by geolocation, user type) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.cumulativeLayoutShift.load.browser", "displayName": "Cumulative Layout Shift - load action (by browser) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.domInteractive.load.browser", "displayName": "DOM interactive - load action (by browser) [web]", "description": "The time taken until a page's status is set to \"interactive\" and it's ready to receive user input. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.endedSessions", "displayName": "Session count - estimated ended sessions (by users, user type) [web]", "description": "The number of completed user sessions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.event.count.rageClick", "displayName": "Rage click count [web]", "description": "The number of detected rage clicks.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.firstByte.load.browser", "displayName": "Time to first byte - load action (by browser) [web]", "description": "The time taken until the first byte of the response is received from the server, relevant application caches, or a local resource. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.firstByte.xhr.browser", "displayName": "Time to first byte - XHR action (by browser) [web]", "description": "The time taken until the first byte of the response is received from the server, relevant application caches, or a local resource. Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.firstInputDelay.load.userType", "displayName": "First Input Delay - load action (by user type) [web]", "description": "The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.firstInputDelay.load.browser", "displayName": "First Input Delay - load action (by browser) [web]", "description": "The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.largestContentfulPaint.load.userType", "displayName": "Largest Contentful Paint - load action (by user type) [web]", "description": "The time taken to render the largest element in the viewport. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.largestContentfulPaint.load.browser", "displayName": "Largest Contentful Paint - load action (by browser) [web]", "description": "The time taken to render the largest element in the viewport. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.loadEventEnd.load.browser", "displayName": "Load event end - load action (by browser) [web]", "description": "The time taken to complete the load event of a page. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.loadEventStart.load.browser", "displayName": "Load event start - load action (by browser) [web]", "description": "The time taken to begin the load event of a page. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.networkContribution.load", "displayName": "Network contribution - load action (by user type) [web]", "description": "The time taken to request and receive resources (including DNS lookup, redirect, and TCP connect time). Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.networkContribution.xhr", "displayName": "Network contribution - XHR action (by user type) [web]", "description": "The time taken to request and receive resources (including DNS lookup, redirect, and TCP connect time). Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.responseEnd.load.browser", "displayName": "Response end - load action (by browser) [web]", "description": "(AKA HTML downloaded) The time taken until the user agent receives the last byte of the response or the transport connection is closed, whichever comes first. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.responseEnd.xhr.browser", "displayName": "Response end - XHR action (by browser) [web]", "description": "The time taken until the user agent receives the last byte of the response or the transport connection is closed, whichever comes first. Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.serverContribution.load", "displayName": "Server contribution - load action (by user type) [web]", "description": "The time spent on server-side processing for a page. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.serverContribution.xhr", "displayName": "Server contribution - XHR action (by user type) [web]", "description": "The time spent on server-side processing for a page. Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.sessionDuration", "displayName": "Session duration (by users, user type) [web]", "description": "The average duration of user sessions.", "unit": "MicroSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.speedIndex.load.browser", "displayName": "Speed index - load action (by browser) [web]", "description": "The score measuring how quickly the visible parts of a page are rendered. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.startedSessions", "displayName": "Session count - estimated started sessions (by users, user type) [web]", "description": "The number of started user sessions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.visuallyComplete.load.browser", "displayName": "Visually complete - load action (by browser) [web]", "description": "The time taken to fully render content in the viewport. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.visuallyComplete.xhr.browser", "displayName": "Visually complete - XHR action (by browser) [web]", "description": "The time taken to fully render content in the viewport. Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.countOfErrors", "displayName": "Error count (by user type, error type, error origin) [web]", "description": "The number of detected errors.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.countOfErrorsDuringUserActions", "displayName": "Error count during user actions (by user type, error type, error origin) [web]", "description": "The number of detected errors that occurred during user actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.countOfStandaloneErrors", "displayName": "Standalone error count (by user type, error type, error origin) [web]", "description": "The number of detected standalone errors (occurred between user actions).", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.countOfUserActionsWithErrors", "displayName": "User action count - with errors (by user type) [web]", "description": "The number of key user actions with detected errors.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.errorCountForDavis", "displayName": "Error count for <PERSON> (by user type, error type, error origin, error context)) [web]", "description": "The number of errors that were included in Davis AI problem detection and analysis.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}, {"key": "Error context", "name": "Error context", "displayName": "Error context", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.interactionToNextPaint", "displayName": "Interaction to next paint", "description": "", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "count", "max", "median", "min", "percentile"], "defaultAggregation": {"type": "percentile", "parameter": 50.0}, "dimensionDefinitions": [{"key": "User type", "name": null, "displayName": "User type", "index": null, "type": "STRING"}, {"key": "dt.entity.application", "name": null, "displayName": "Web application", "index": null, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": null, "displayName": "Browser", "index": null, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": null, "displayName": "Geolocation", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.jsErrorsDuringUa", "displayName": "JavaScript error count - during user actions (by user type) [web]", "description": "The number of detected JavaScript errors that occurred during user actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.jsErrorsWithoutUa", "displayName": "JavaScript error count - without user actions (by user type) [web]", "description": "The number of detected standalone JavaScript errors (occurred between user actions).", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.percentageOfUserActionsAffectedByErrors", "displayName": "User action rate - affected by errors (by user type) [web]", "description": "The percentage of user actions with detected errors.", "unit": "Percent", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.apps.mobile.userActionPropertiesByMobileApplication", "displayName": "Total user action and session properties", "description": "The number of billed user action and user session properties.", "unit": "Count", "entityType": ["MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.mobile_application", "name": "Mobile application", "displayName": "Mobile App", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.apps.web.userActionPropertiesByApplication", "displayName": "Total user action and session properties", "description": "The number of billed user action and user session properties.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.custom_events_classic.usage", "displayName": "(DPS) Total Custom Events Classic billing usage", "description": "The number of custom events ingested aggregated over all monitored entities. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_events_classic.usage_by_entity", "displayName": "(DPS) Custom Events Classic billing usage by monitored entity", "description": "The number of custom events ingested split by monitored entity. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. For details on the events billed, refer to the usage_by_event_info metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.custom_events_classic.usage_by_event_info", "displayName": "(DPS) Custom Events Classic billing usage by event info", "description": "The number of custom events ingested split by event info. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. The info contains the context of the event plus the configuration ID. For details on the related monitored entities, refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "event_info", "name": "event_info", "displayName": "event_info", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.raw.usage_by_metric_key", "displayName": "(DPS) Recorded metric data points per metric key", "description": "The number of reported metric data points split by metric key. This metric does not account for included metric data points available to your environment.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "metric_key", "name": "metric_key", "displayName": "metric_key", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage", "displayName": "(DPS) Total billed metric data points", "description": "The total number of metric data points after deducting the included metric data points. This is the rate-card value used for billing. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.foundation_and_discovery", "displayName": "(DPS) Total metric data points billable for Foundation & Discovery hosts", "description": "The number of metric data points billable for Foundation & Discovery hosts.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.fullstack_hosts", "displayName": "(DPS) Total metric data points billed for Full-Stack hosts", "description": "The number of metric data points billed for Full-Stack hosts. To view the unadjusted usage per host, use builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host . This trailing metric is reported at 15-minute intervals with up to a 15-minute delay.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.infrastructure_hosts", "displayName": "(DPS) Total metric data points billed for Infrastructure-monitored hosts", "description": "The number of metric data points billed for Infrastructure-monitored hosts. To view the unadjusted usage per host, use builtin:billing.infrastructure_monitoring.metric_data_points.ingested_by_host . This trailing metric is reported at 15-minute intervals with up to a 15-minute delay.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.other", "displayName": "(DPS) Total metric data points billed by other entities", "description": "The number of metric data points billed that cannot be assigned to a host. The values reported in this metric are not eligible for included metric deduction and will be billed as is. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. o view the monitored entities that consume this usage, use the other_by_entity metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.other_by_entity", "displayName": "(DPS) Billed metric data points reported and split by other entities", "description": "The number of billed metric data points split by entities that cannot be assigned to a host. The values reported in this metric are not eligible for included metric deduction and will be billed as is. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.events.business_events.ingest.usage", "displayName": "[Deprecated]  (DPS) Business events usage - Ingest & Process", "description": "Business events Ingest & Process usage, tracked as bytes ingested within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.events.business_events.query.usage", "displayName": "[Deprecated] (DPS) Business events usage - Query", "description": "Business events Query usage, tracked as bytes read within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.events.business_events.retain.usage", "displayName": "[Deprecated] (DPS) Business events usage - Retain", "description": "Business events Retain usage, tracked as total storage used within the hour, in bytes. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.foundation_and_discovery.metric_data_points.ingested", "displayName": "(DPS) Ingested metric data points for Foundation & Discovery", "description": "The number of metric data points aggregated over all Foundation & Discovery hosts.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.foundation_and_discovery.usage", "displayName": "(DPS) Foundation & Discovery billing usage", "description": "The total number of host-hours being monitored by Foundation & Discovery, counted in 15 min intervals.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.metric_data_points.included", "displayName": "(DPS) Available included metric data points for Full-Stack hosts", "description": "The total number of included metric data points that can be deducted from the metric data points reported by Full-Stack hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of applied included metric data points, use builtin:billing.full_stack_monitoring.metric_data_points.included_used . If the difference between this metric and the applied metrics is greater than 0, then more metrics can be ingested using Full-Stack Monitoring without incurring additional costs.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.metric_data_points.included_used", "displayName": "(DPS) Used included metric data points for Full-Stack hosts", "description": "The number of consumed included metric data points per host monitored with Full-Stack Monitoring. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of potentially available included metrics, use builtin:billing.full_stack_monitoring.metric_data_points.included_used . If the difference between this metric and the available metrics is greater than zero, then that means that more metrics could be ingested on Full-Stack hosts without incurring additional costs.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.metric_data_points.ingested", "displayName": "(DPS) Total metric data points reported by Full-Stack hosts", "description": "The number of metric data points aggregated over all Full-Stack hosts. The values reported in this metric are eligible for included-metric-data-point deduction. Use this total metric to query longer timeframes without losing precision or performance. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view usage on a per-host basis, use builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host .", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host", "displayName": "(DPS) Metric data points reported and split by Full-Stack hosts", "description": "The number of metric data points split by Full-Stack hosts. The values reported in this metric are eligible for included-metric-data-point deduction. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. The pool of available included metrics for a \"15-minute interval\" is visible via builtin:billing.full_stack_monitoring.metric_data_points.included . To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.usage", "displayName": "(DPS) Full-Stack Monitoring billing usage", "description": "The total GiB memory of hosts being monitored in full-stack mode, counted in 15 min intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the hosts causing the usage, refer to the usage_per_host metric. For details on the containers causing the usage, refer to the usage_per_container metric.", "unit": "GibiByte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.usage_per_container", "displayName": "(DPS) Full-stack usage by container type", "description": "The total GiB memory of containers being monitored in full-stack mode, counted in 15 min intervals.", "unit": "GibiByte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "application_only_type", "name": null, "displayName": "application_only_type", "index": null, "type": "STRING"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.usage_per_host", "displayName": "(DPS) Full-Stack Monitoring billing usage per host", "description": "The GiB memory per host being monitored in full-stack mode, counted in 15 min intervals. For example, a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "GibiByte", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.metric_data_points.included", "displayName": "(DPS) Available included metric data points for Infrastructure-monitored hosts", "description": "The total number of included metric data points that can be deducted from the metric data points reported by Infrastructure-monitored hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of applied included metric data points, use builtin:billing.infrastructure_monitoring.metric_data_points.included_used . If the difference between this metric and the applied metrics is greater than zero, then that means that more metrics could be ingested on Infrastructure-monitored hosts without incurring additional costs.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.metric_data_points.included_used", "displayName": "(DPS) Used included metric data points for Infrastructure-monitored hosts", "description": "The number of consumed included metric data points for Infrastructure-monitored hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of potentially available included metrics, use builtin:billing.infrastructure_monitoring.metric_data_points.included_used . If the difference between this metric and the available metrics is greater than zero, then that means that more metrics could be ingested on Infrastructure-monitored hosts without incurring additional costs.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.metric_data_points.ingested", "displayName": "(DPS) Total metric data points reported by Infrastructure-monitored hosts", "description": "The number of metric data points aggregated over all Infrastructure-monitored hosts. The values reported in this metric are eligible for included-metric-data-point deduction. Use this total metric to query longer timeframes without losing precision or performance. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view usage on a per-host basis, use the builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host .", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.usage", "displayName": "(DPS) Infrastructure Monitoring billing usage", "description": "The total number of host-hours being monitored in infrastructure-only mode, counted in 15 min intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the hosts causing the usage, refer to the usage_per_host metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.kubernetes_monitoring.usage", "displayName": "(DPS) Kubernetes Platform Monitoring billing usage", "description": "The total number of monitored Kubernetes pods per hour, split by cluster and namespace and counted in 15 min intervals. A pod monitored for the whole hour has 4 data points with a value of 0.25.", "unit": "Count", "entityType": ["KUBERNETES_CLUSTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": "Kubernetes cluster", "displayName": "Kubernetes cluster", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": "Cloud application namespace", "displayName": "Kubernetes namespace", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.log.ingest.usage", "displayName": "(DPS) Log Management and Analytics usage - Ingest & Process", "description": "Log Management and Analytics Ingest & Process usage, tracked as bytes ingested within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.log.query.usage", "displayName": "(DPS) Log Management and Analytics usage - Query", "description": "Log Management and Analytics Query usage, tracked as bytes read within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.log.retain.usage", "displayName": "(DPS) Log Management and Analytics usage - Retain", "description": "Log Management and Analytics Retain usage, tracked as total storage used within the hour, in bytes. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.property.usage", "displayName": "(DPS) Total Real-User Monitoring Property (mobile) billing usage", "description": "(Mobile) User action and session properties count. For details on how usage is calculated, refer to the documentation or builtin:billing.real_user_monitoring.web.property.usage_by_application . Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.session.usage", "displayName": "(DPS) Total Real-User Monitoring (mobile) billing usage", "description": "(Mobile) Session count without Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute, then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage, refer to the usage_by_app metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.session.usage_by_app", "displayName": "(DPS) Real-User Monitoring (mobile) billing usage by application", "description": "(Mobile) Session count without Session Replay split by application The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute, then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.property.usage", "displayName": "(DPS) Total Real-User Monitoring Property (web) billing usage", "description": "(Web) User action and session properties count. For details on how usage is calculated, refer to the documentation or builtin:billing.real_user_monitoring.web.property.usage_by_application . Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.property.usage_by_application", "displayName": "(DPS) Real-User Monitoring Property (web) billing usage by application", "description": "(Web) User action and session properties count by application. The billed value is calculated based on the number of sessions reported in builtin:billing.real_user_monitoring.web.session.usage_by_app + builtin:billing.real_user_monitoring.web.session_with_replay.usage_by_app . plus the number of configured properties that exceed the included number of properties (free of charge) offered for a given application. Data points are only written for billed sessions. If the value is 0, you have available metric data points. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.session.usage", "displayName": "(DPS) Total Real-User Monitoring (web) billing usage", "description": "(Web) Session count without Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute, then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage, refer to the usage_by_app metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.session.usage_by_app", "displayName": "(DPS) Real-User Monitoring (web) billing usage by application", "description": "(Web) Session count without Session Replay split by application. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute, then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.runtime_application_protection.usage", "displayName": "(DPS) Runtime Application Protection billing usage", "description": "Total GiB-memory of hosts protected with Runtime Application Protection (Application Security), counted at 15-minute intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the monitored hosts, refer to the usage_per_host metric.", "unit": "GibiByte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.runtime_application_protection.usage_per_host", "displayName": "(DPS) Runtime Application Protection billing usage per host", "description": "GiB-memory per host protected with Runtime Application Protection (Application Security), counted at 15-minute intervals. For example, a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "GibiByte", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.runtime_vulnerability_analytics.usage", "displayName": "(DPS) Runtime Vulnerability Analytics billing usage", "description": "Total GiB-memory of hosts protected with Runtime Vulnerability Analytics (Application Security), counted at 15-minute intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the monitored hosts, refer to the usage_per_host metric.", "unit": "GibiByte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.runtime_vulnerability_analytics.usage_per_host", "displayName": "(DPS) Runtime Vulnerability Analytics billing usage per host", "description": "GiB-memory per hosts protected with Runtime Vulnerability Analytics (Application Security), counted at 15-minute intervals. For example, a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "GibiByte", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.connections.active", "displayName": "ALB number of active connections", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.connections.new", "displayName": "ALB number of new connections", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.alb.http4xx", "displayName": "ALB number of 4XX errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.alb.http5xx", "displayName": "ALB number of 5XX errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.target.http4xx", "displayName": "ALB number of 4XX target errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.target.http5xx", "displayName": "ALB number of 5XX target errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.targConn", "displayName": "ALB number of target connection errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.tlsNeg", "displayName": "ALB number of client TLS negotiation errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.bytes", "displayName": "ALB number of processed bytes", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.lcus", "displayName": "ALB number of consumed LCUs", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.requests", "displayName": "ALB number of requests", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.respTime", "displayName": "ALB target response time", "description": "", "unit": "Second", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.az.running", "displayName": "Number of running EC2 instances (AZ)", "description": "", "unit": "Count", "entityType": ["AWS_CREDENTIALS"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_credentials", "name": "AWS account", "displayName": "AWS Credentials", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.aws_availability_zone", "name": "AWS Availability Zone", "displayName": "AWS Availability Zone", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.az.stopped", "displayName": "Number of stopped EC2 instances (AZ)", "description": "", "unit": "Count", "entityType": ["AWS_CREDENTIALS"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_credentials", "name": "AWS account", "displayName": "AWS Credentials", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.aws_availability_zone", "name": "AWS Availability Zone", "displayName": "AWS Availability Zone", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.az.terminated", "displayName": "Number of terminated EC2 instances (AZ)", "description": "", "unit": "Count", "entityType": ["AWS_CREDENTIALS"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_credentials", "name": "AWS account", "displayName": "AWS Credentials", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.aws_availability_zone", "name": "AWS Availability Zone", "displayName": "AWS Availability Zone", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.consumed.read", "displayName": "DynamoDB read capacity units", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.consumed.write", "displayName": "DynamoDB write capacity units", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.read", "displayName": "DynamoDB read capacity units %", "description": "", "unit": "Percent", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.write", "displayName": "DynamoDB write capacity units %", "description": "", "unit": "Percent", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.requests.latency", "displayName": "DynamoDB number of successful request latency for operation", "description": "", "unit": "MilliSecond", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}, {"key": "Operation", "name": "Operation", "displayName": "Operation", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.requests.returnedItems", "displayName": "DynamoDB number of items returned by operation", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}, {"key": "Operation", "name": "Operation", "displayName": "Operation", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.tables", "displayName": "Number of tables for AvailabilityZone", "description": "", "unit": "Count", "entityType": ["AWS_CREDENTIALS"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_credentials", "name": "AWS account", "displayName": "AWS Credentials", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.aws_availability_zone", "name": "AWS Availability Zone", "displayName": "AWS Availability Zone", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.connection", "displayName": "CLB backend connection errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.http2xx", "displayName": "CLB number of backend 2XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.http3xx", "displayName": "CLB number of backend 3XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.http4xx", "displayName": "CLB number of backend 4XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.elb.http5xx", "displayName": "CLB number of 5XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.hosts.healthy", "displayName": "CLB number of healthy hosts", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.hosts.unhealthy", "displayName": "CLB number of unhealthy hosts", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.latency", "displayName": "CLB latency", "description": "", "unit": "Second", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.reqCompl", "displayName": "CLB number of completed requests", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.flow.active", "displayName": "NLB number of active flows", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.flow.new", "displayName": "NLB number of new flows", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.tcp.reset.client", "displayName": "NLB number of client resets", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.tcp.reset.elb", "displayName": "NLB number of resets", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.tcp.reset.target", "displayName": "NLB number of target resets", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.bytes", "displayName": "NLB number of processed bytes", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.lcus", "displayName": "NLB number of consumed LCUs", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.cpu.usage", "displayName": "RDS CPU usage %", "description": "", "unit": "Percent", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.latency.read", "displayName": "RDS read latency", "description": "", "unit": "Second", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.latency.write", "displayName": "RDS write latency", "description": "", "unit": "Second", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.memory.freeable", "displayName": "RDS freeable memory", "description": "", "unit": "Byte", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.memory.swap", "displayName": "RDS swap usage", "description": "", "unit": "Byte", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.net.rx", "displayName": "RDS network received throughput", "description": "", "unit": "BytePerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.net.tx", "displayName": "RDS network transmitted throughput", "description": "", "unit": "BytePerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.ops.read", "displayName": "RDS read IOPS", "description": "", "unit": "PerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.ops.write", "displayName": "RDS write IOPS", "description": "", "unit": "PerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.throughput.read", "displayName": "RDS read throughput", "description": "", "unit": "BytePerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.throughput.write", "displayName": "RDS write throughput", "description": "", "unit": "BytePerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.connections", "displayName": "RDS connections", "description": "", "unit": "Count", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.free", "displayName": "RDS free storage space %", "description": "", "unit": "Percent", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.restarts", "displayName": "RDS restarts", "description": "", "unit": "Count", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.cloudfoundry.http.badGateways", "displayName": "CF: 502 responses", "description": "The number of responses that indicate invalid service responses produced by an application.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:cloud.cloudfoundry.http.latency", "displayName": "CF: Response latency", "description": "The average response time from the application to clients.", "unit": "MilliSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:cloud.cloudfoundry.http.responses5xx", "displayName": "CF: 5xx responses", "description": "The number of responses that indicate repeatedly crashing apps or response issues from applications.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:cloud.cloudfoundry.http.totalRequests", "displayName": "CF: Total requests", "description": "The number of all requests representing the overall traffic flow.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:containers.cpu.limit", "displayName": "Containers: CPU limit, mCores", "description": "CPU resource limit per container in millicores.", "unit": "MilliCores", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.logicalCores", "displayName": "Containers: CPU logical cores", "description": "Number of logical CPU cores of the host.", "unit": "Cores", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.shares", "displayName": "Containers: CPU shares", "description": "Number of CPU shares allocated per container.", "unit": "Count", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.throttledMilliCores", "displayName": "Containers: CPU throttling, mCores", "description": "CPU throttling per container in millicores.", "unit": "MilliCores", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.throttledTime", "displayName": "Containers: CPU throttled time, ns/min", "description": "Total amount of time a container has been throttled, in nanoseconds per minute.", "unit": "NanoSecondPerMinute", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.usageMilliCores", "displayName": "Containers: CPU usage, mCores", "description": "CPU usage per container in millicores", "unit": "MilliCores", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.usagePercent", "displayName": "Containers: CPU usage, % of limit", "description": "Percent CPU usage per container relative to CPU resource limit. Logical cores are used if CPU limit isn't set.", "unit": "Percent", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.usageSystemMilliCores", "displayName": "Containers: CPU system usage, mCores", "description": "CPU system usage per container in millicores.", "unit": "MilliCores", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.usageSystemTime", "displayName": "Containers: CPU system usage time, ns/min", "description": "Used system time per container in nanoseconds per minute.", "unit": "NanoSecondPerMinute", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.usageTime", "displayName": "Containers: CPU usage time, ns/min", "description": "Sum of used system and user time per container in nanoseconds per minute.", "unit": "NanoSecondPerMinute", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.usageUserMilliCores", "displayName": "Containers: CPU user usage, mCores", "description": "CPU user usage per container in millicores.", "unit": "MilliCores", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.cpu.usageUserTime", "displayName": "Containers: CPU user usage time, ns/min", "description": "Used user time per container in nanoseconds per minute.", "unit": "NanoSecondPerMinute", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.memory.cacheBytes", "displayName": "Containers: Memory cache, bytes", "description": "Page cache memory per container in bytes.", "unit": "Byte", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.memory.limitBytes", "displayName": "Containers: Memory limit, bytes", "description": "Memory limit per container in bytes. If no limit is set, this is an empty value.", "unit": "Byte", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.memory.limitPercent", "displayName": "Containers: Memory limit, % of physical memory", "description": "Percent memory limit per container relative to total physical memory. If no limit is set, this is an empty value.", "unit": "Percent", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.memory.physicalTotalBytes", "displayName": "Containers: Memory - total physical memory, bytes", "description": "Total physical memory on the host in bytes.", "unit": "Byte", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.memory.residentSetBytes", "displayName": "Containers: Memory usage, bytes", "description": "Resident set size (Linux) or private working set size (Windows) per container in bytes.", "unit": "Byte", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:containers.memory.usagePercent", "displayName": "Containers: Memory usage, % of limit", "description": "Resident set size (Linux) or private working set size (Windows) per container in percent relative to container memory limit. If no limit is set, this equals total physical memory.", "unit": "Percent", "entityType": ["CONTAINER_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.container_group_instance", "name": "Container group instance", "displayName": "Container group instance", "index": 0, "type": "ENTITY"}, {"key": "Container", "name": "Container", "displayName": "Container", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:host.availability", "displayName": "Host availability %", "description": "Host availability %", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.availability.state", "displayName": "Host availability", "description": "Host availability state metric reported in 1 minute intervals", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "availability.state", "name": null, "displayName": "availability.state", "index": null, "type": "STRING"}, {"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.metrics.source", "name": null, "displayName": "dt.metrics.source", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:host.cpu.idle", "displayName": "CPU idle", "description": "Average CPU time, when the CPU didn't have anything to do", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.iowait", "displayName": "CPU I/O wait", "description": "Percentage of time when CPU was idle during which the system had an outstanding I/O request. It is not available on Windows.", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.load", "displayName": "System load", "description": "The average number of processes that are being executed by CPU or waiting to be executed by CPU over the last minute", "unit": "<PERSON><PERSON>", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.load15m", "displayName": "System load15m", "description": "The average number of processes that are being executed by CPU or waiting to be executed by CPU over the last 15 minutes", "unit": "<PERSON><PERSON>", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.load5m", "displayName": "System load5m", "description": "The average number of processes that are being executed by CPU or waiting to be executed by CPU over the last 5 minutes", "unit": "<PERSON><PERSON>", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.other", "displayName": "CPU other", "description": "Average CPU time spent on other tasks: servicing interrupt requests (IRQ), running virtual machines under the control of the host's kernel (meaning the host is a hypervisor for VMs). It's available only for Linux hosts", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.steal", "displayName": "CPU steal", "description": "Average CPU time, when a virtual machine waits to get CPU cycles from the hypervisor. In a virtual environment, CPU cycles are shared across virtual machines on the hypervisor server. If your virtualized host displays a high CPU steal, it means CPU cycles are being taken away from your virtual machine to serve other purposes. It may indicate an overloaded hypervisor. It's available only for Linux hosts", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.system", "displayName": "CPU system", "description": "Average CPU time when CPU was running in kernel mode", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.usage", "displayName": "CPU usage %", "description": "Percentage of CPU time when CPU was utilized. A value close to 100% means most host processing resources are in use, and host CPUs can't handle additional work", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.cpu.user", "displayName": "CPU user", "description": "Average CPU time when CPU was running in user mode", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.throughput.read", "displayName": "Disk throughput read", "description": "File system read throughput in bits per second", "unit": "BitPerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.throughput.write", "displayName": "Disk throughput write", "description": "File system write throughput in bits per second", "unit": "BitPerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.avail", "displayName": "Disk available", "description": "Amount of free space available for user in file system. On Linux and AIX it is free space available for unprivileged user. It doesn't contain part of free space reserved for the root.", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.bytesRead", "displayName": "Disk read bytes per second", "description": "Speed of read from file system in bytes per second", "unit": "BytePerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.bytesWritten", "displayName": "Disk write bytes per second", "description": "Speed of write to file system in bytes per second", "unit": "BytePerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.free", "displayName": "Disk available %", "description": "Percentage of free space available for user in file system. On Linux and AIX it is % of free space available for unprivileged user. It doesn't contain part of free space reserved for the root.", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.inodesAvail", "displayName": "Inodes available %", "description": "Percentage of free inodes available for unprivileged user in file system. Metric not available on Windows.", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.inodesTotal", "displayName": "Inodes total", "description": "Total amount of inodes available for unprivileged user in file system. Metric not available on Windows.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.queueLength", "displayName": "Disk average queue length", "description": "Average number of read and write operations in disk queue", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.readOps", "displayName": "Disk read operations per second", "description": "Number of read operations from file system per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.readTime", "displayName": "<PERSON><PERSON> read time", "description": "Average time of read from file system. It shows average disk latency during read.", "unit": "MilliSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.used", "displayName": "Disk used", "description": "Amount of used space in file system", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.usedPct", "displayName": "Disk used %", "description": "Percentage of used space in file system", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.utilTime", "displayName": "Disk utilization time", "description": "Percent of time spent on disk I/O operations", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.writeOps", "displayName": "Disk write operations per second", "description": "Number of write operations to file system per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.disk.writeTime", "displayName": "Disk write time", "description": "Average time of write to file system. It shows average disk latency during write.", "unit": "MilliSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.disk", "name": "Disk", "displayName": "Disk", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.dns.errorCount", "displayName": "Number of DNS errors by type", "description": "Number of DNS errors by type", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dnsServerIp", "name": "dnsServerIp", "displayName": "dnsServerIp", "index": 1, "type": "STRING"}, {"key": "errorType", "name": "errorType", "displayName": "errorType", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:host.dns.queryCount", "displayName": "Number of DNS queries", "description": "Number of DNS queries on the host", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dnsServerIp", "name": "dnsServerIp", "displayName": "dnsServerIp", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:host.dns.queryTime", "displayName": "DNS query time sum", "description": "The time of all DNS queries on the host", "unit": "MilliSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dnsServerIp", "name": "dnsServerIp", "displayName": "dnsServerIp", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:host.dns.singleQueryTime", "displayName": "DNS query time", "description": "Average time of DNS query. Calculated with DNS query time sum divided by number of DNS queries for each host and DNS server pair.", "unit": "MilliSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dnsServerIp", "name": null, "displayName": "dnsServerIp", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:host.dns.singleQueryTimeByDnsIp", "displayName": "DNS query time by DNS server", "description": "The weighted average time of DNS query by DNS server ip. Calculated with DNS query time sum divided by number of DNS queries. It weights the result taking into account number of requests from each host.", "unit": "MilliSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dnsServerIp", "name": null, "displayName": "dnsServerIp", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:host.dns.singleQueryTimeByHost", "displayName": "DNS query time on host", "description": "The weighted average time of DNS query on a host. Calculated with DNS query time sum divided by number of DNS queries on a host. It weights the result taking into account number of requests to each dns server", "unit": "MilliSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.handles.fileDescriptorsMax", "displayName": "File descriptors max", "description": "Maximum amount of file descriptors for use", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.handles.fileDescriptorsUsed", "displayName": "File descriptors used", "description": "Amount of file descriptors used", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.avail.bytes", "displayName": "Memory available", "description": "The amount of memory (RAM) available on the host. The memory that is available for allocation to new or existing processes. Available memory is an estimation of how much memory is available for use without swapping.", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.avail.pct", "displayName": "Memory available %", "description": "The percentage of memory (RAM) available on the host. The memory that is available for allocation to new or existing processes. Available memory is an estimation of how much memory is available for use without swapping. Shows available memory as percentages.", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.avail.pfps", "displayName": "Page faults per second", "description": "The measure of the number of page faults per second on the monitored host. This value includes soft faults and hard faults.", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.swap.avail", "displayName": "Swap available", "description": "The amount of swap memory or swap space (also known as paging, which is the on-disk component of the virtual memory system) available.", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.swap.total", "displayName": "Swap total", "description": "Amount of total swap memory or total swap space (also known as paging, which is the on-disk component of the virtual memory system) for use.", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.swap.used", "displayName": "Swap used", "description": "The amount of swap memory or swap space (also known as paging, which is the on-disk component of the virtual memory system) used.", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.recl", "displayName": "Memory reclaimable", "description": "The memory usage for specific purposes. Reclaimable memory is calculated as available memory (estimation of how much memory is available for use without swapping) minus free memory (amount of memory that is currently not used for anything). For more information on reclaimable memory, see [this blog post](https://www.dynatrace.com/news/blog/improved-host-memory-metrics-now-include-reclaimable-memory/).", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.total", "displayName": "Memory total", "description": "The amount of memory (RAM) installed on the system.", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.usage", "displayName": "Memory used %", "description": "Shows percentage of memory currently used. Used memory is calculated by OneAgent as follows: used = total - available. So the used memory metric displayed in Dynatrace analysis views is not equal to the used memory metric displayed by system tools. At the same time, it's important to remember that system tools report used memory the way they do due to historical reasons, and that this particular method of calculating used memory isn't really representative of how the Linux kernel manages memory in modern systems. The difference in these measurements is in fact quite significant, too. Note: Calculated by taking 100% - \"Memory available %\".", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.mem.used", "displayName": "Memory used", "description": "Used memory is calculated by OneAgent as follows: used = total - available. So the used memory metric displayed in Dynatrace analysis views is not equal to the used memory metric displayed by system tools. At the same time, it's important to remember that system tools report used memory the way they do due to historical reasons, and that this particular method of calculating used memory isn't really representative of how the Linux kernel manages memory in modern systems. The difference in these measurements is in fact quite significant, too.", "unit": "Byte", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.packets.dropped", "displayName": "NIC packets dropped", "description": "Network interface packets dropped on the host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": null, "displayName": "Network Interface", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.packets.droppedRx", "displayName": "NIC received packets dropped", "description": "Network interface received packets dropped on the host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.packets.droppedTx", "displayName": "NIC sent packets dropped", "description": "Network interface sent packets dropped on the host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.packets.errors", "displayName": "NIC packet errors", "description": "Network interface packet errors on the host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": null, "displayName": "Network Interface", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.packets.errorsRx", "displayName": "NIC received packet errors", "description": "Network interface received packet errors on a host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.packets.errorsTx", "displayName": "NIC sent packet errors", "description": "Network interface sent packet errors on the host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.packets.rx", "displayName": "NIC packets received", "description": "Network interface packets received on the host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.packets.tx", "displayName": "NIC packets sent", "description": "Network interface packets sent on the host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.bytesRx", "displayName": "NIC bytes received", "description": "Network interface bytes received on the host", "unit": "BytePerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.bytesTx", "displayName": "NIC bytes sent on host", "description": "Network interface bytes sent on the host", "unit": "BytePerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.connectivity", "displayName": "NIC connectivity", "description": "Network interface connectivity on the host", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": null, "displayName": "Network Interface", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.connectivityNew", "displayName": "NIC connectivity", "description": "Network interface connectivity on the host", "unit": "Percent", "entityType": [], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:host.net.nic.linkUtilRx", "displayName": "NIC receive link utilization", "description": "Network interface receive link utilization on the host", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.linkUtilTx", "displayName": "NIC transmit link utilization", "description": "Network interface transmit link utilization on the host", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.retransmission", "displayName": "NIC retransmission", "description": "Network interface retransmission on the host", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": null, "displayName": "Network Interface", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.retransmissionIn", "displayName": "NIC received packets retransmission", "description": "Network interface retransmission for received packets on the host", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": null, "displayName": "Network Interface", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.retransmissionOut", "displayName": "NIC sent packets retransmission", "description": "Network interface retransmission for sent packets on the host", "unit": "Percent", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": null, "displayName": "Network Interface", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.traffic", "displayName": "Traffic", "description": "Network traffic on the host", "unit": "BitPerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": null, "displayName": "Network Interface", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.trafficIn", "displayName": "Traffic in", "description": "Traffic incoming at the host", "unit": "BitPerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.nic.trafficOut", "displayName": "Traffic out", "description": "Traffic outgoing from the host", "unit": "BitPerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.packets.rxBaseReceived", "displayName": "Host retransmission base received", "description": "Host aggregated process retransmission base received per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.packets.rxBaseSent", "displayName": "Host retransmission base sent", "description": "Host aggregated process retransmission base sent per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.packets.rxReceived", "displayName": "Host retransmitted packets received", "description": "Host aggregated process retransmitted packets received per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.packets.rxSent", "displayName": "Host retransmitted packets sent", "description": "Host aggregated process retransmitted packets sent per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.sessions.local.errRst", "displayName": "Localhost session reset received", "description": "Host aggregated session reset received per second on localhost", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.sessions.local.errTmout", "displayName": "Localhost session timeout received", "description": "Host aggregated session timeout received per second on localhost", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.sessions.local.new", "displayName": "Localhost new session received", "description": "Host aggregated new session received per second on localhost", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.sessions.errRst", "displayName": "Host session reset received", "description": "Host aggregated process session reset received per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.sessions.errTmout", "displayName": "Host session timeout received", "description": "Host aggregated process session timeout received per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.sessions.new", "displayName": "Host new session received", "description": "Host aggregated process new session received per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.bytesRx", "displayName": "Host bytes received", "description": "Host aggregated process bytes received per second", "unit": "BytePerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.net.bytesTx", "displayName": "Host bytes sent", "description": "Host aggregated process bytes sent per second", "unit": "BytePerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.osProcessStats.osProcessCount", "displayName": "OS Process count", "description": "This metric shows an average number of processes, over one minute, running on the host. The reported number of processes is based on processes detected by the OS module, read in 10 seconds cycles.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.osProcessStats.pgiCount", "displayName": "PGI count", "description": "This metric shows the number of PGIs created by the OS module every minute. It includes every PGI, even those which are considered not important and are not reported to Dynatrace.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.osProcessStats.pgiReportedCount", "displayName": "Reported PGI count", "description": "This metric shows the number of PGIs created and reported by the OS module every minute. It includes only PGIs, which are considered important and reported to Dynatrace. Important PGIs are those in which OneAgent recognizes the technology, have open network ports, generate significant resource usage, or are created via Declarative process grouping rules. To learn what makes process important, see [Which are the most important processes?](https://dt-url.net/most-important-processes)", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:host.uptime", "displayName": "Host uptime", "description": "Time since last host boot up. Requires OneAgent 1.259+. The metric is not supported for application-only OneAgent deployments.", "unit": "Second", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.metrics.source", "name": null, "displayName": "dt.metrics.source", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.cluster.readyz", "displayName": "Kubernetes: Cluster readyz status", "description": "Current status of the Kubernetes API server reported by the /readyz endpoint (0 or 1).", "unit": "Unspecified", "entityType": ["KUBERNETES_CLUSTER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.container.oom_kills", "displayName": "Kubernetes: Container - out of memory (OOM) kill count", "description": "This metric measures the out of memory (OOM) kills. The most detailed level of aggregation is container. The value corresponds to the status 'OOMKilled' of a container in the pod resource's container status. The metric is only written if there was at least one container OOM kill.", "unit": "Count", "entityType": ["CLOUD_APPLICATION_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_instance", "name": null, "displayName": "Kubernetes pod", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.container.name", "name": null, "displayName": "k8s.container.name", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.uid", "name": null, "displayName": "k8s.pod.uid", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}, {"key": "last_exit_code", "name": null, "displayName": "last_exit_code", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.container.restarts", "displayName": "Kubernetes: Container - restart count", "description": "This metric measures the amount of container restarts. The most detailed level of aggregation is container. The value corresponds to the delta of the 'restartCount' defined in the pod resource's container status. The metric is only written if there was at least one container restart.", "unit": "Count", "entityType": ["CLOUD_APPLICATION_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_instance", "name": null, "displayName": "Kubernetes pod", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.container.name", "name": null, "displayName": "k8s.container.name", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.uid", "name": null, "displayName": "k8s.pod.uid", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.conditions", "displayName": "Kubernetes: Node conditions", "description": "This metric describes the status of a Kubernetes node. The most detailed level of aggregation is node.", "unit": "Count", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "condition_reason", "name": null, "displayName": "condition_reason", "index": null, "type": "STRING"}, {"key": "condition_status", "name": null, "displayName": "condition_status", "index": null, "type": "STRING"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}, {"key": "node_condition", "name": null, "displayName": "node_condition", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.cpu_allocatable", "displayName": "Kubernetes: Node - CPU allocatable", "description": "This metric measures the total allocatable cpu. The most detailed level of aggregation is node. The value corresponds to the allocatable cpu of a node.", "unit": "MilliCores", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.cpu_usage", "displayName": "Kubernetes: Container - CPU usage (by node)", "description": "This metric measures the total CPU consumed (user usage + system usage) by container. The most detailed level of aggregation is node.", "unit": "MilliCores", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.limits_cpu", "displayName": "Kubernetes: Pod - CPU limits (by node)", "description": "This metric measures the cpu limits. The most detailed level of aggregation is node. The value is the sum of the cpu limits of all app containers of a pod.", "unit": "MilliCores", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.limits_memory", "displayName": "Kubernetes: Pod - memory limits (by node)", "description": "This metric measures the memory limits. The most detailed level of aggregation is node. The value is the sum of the memory limits of all app containers of a pod.", "unit": "Byte", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.memory_allocatable", "displayName": "Kubernetes: Node - memory allocatable", "description": "This metric measures the total allocatable memory. The most detailed level of aggregation is node. The value corresponds to the allocatable memory of a node.", "unit": "Byte", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.memory_working_set", "displayName": "Kubernetes: Container - Working set memory (by node)", "description": "This metric measures the current working set memory (memory that cannot be reclaimed under pressure) by container. The OOM Killer is invoked if the working set exceeds the limit. The most detailed level of aggregation is node.", "unit": "Byte", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.pods", "displayName": "Kubernetes: Pod count (by node)", "description": "This metric measures the number of pods. The most detailed level of aggregation is node. The value corresponds to the count of all pods.", "unit": "Count", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}, {"key": "pod_condition", "name": null, "displayName": "pod_condition", "index": null, "type": "STRING"}, {"key": "pod_phase", "name": null, "displayName": "pod_phase", "index": null, "type": "STRING"}, {"key": "pod_status", "name": null, "displayName": "pod_status", "index": null, "type": "STRING"}, {"key": "pod_status_reason", "name": null, "displayName": "pod_status_reason", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.pods_allocatable", "displayName": "Kubernetes: Node - pod allocatable count", "description": "This metric measures the total number of allocatable pods. The most detailed level of aggregation is node. The value corresponds to the allocatable pods of a node.", "unit": "Count", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.requests_cpu", "displayName": "Kubernetes: Pod - CPU requests (by node)", "description": "This metric measures the cpu requests. The most detailed level of aggregation is node. The value is the sum of the cpu requests of all app containers of a pod.", "unit": "MilliCores", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.node.requests_memory", "displayName": "Kubernetes: Pod - memory requests (by node)", "description": "This metric measures the memory requests. The most detailed level of aggregation is node. The value is the sum of the memory requests of all app containers of a pod.", "unit": "Byte", "entityType": ["KUBERNETES_NODE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.persistentvolumeclaim.available", "displayName": "Kubernetes: PVC - available", "description": "This metric measures the number of available bytes in the volume. The most detailed level of aggregation is persistent volume claim.", "unit": "Byte", "entityType": ["CLOUD_APPLICATION_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.persistent_volume_claim.name", "name": null, "displayName": "k8s.persistent_volume_claim.name", "index": null, "type": "STRING"}, {"key": "k8s.persistentvolumeclaim.name", "name": null, "displayName": "k8s.persistentvolumeclaim.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.persistentvolumeclaim.capacity", "displayName": "Kubernetes: PVC - capacity", "description": "This metric measures the capacity in bytes of the volume. The most detailed level of aggregation is persistent volume claim.", "unit": "Byte", "entityType": ["CLOUD_APPLICATION_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.persistent_volume_claim.name", "name": null, "displayName": "k8s.persistent_volume_claim.name", "index": null, "type": "STRING"}, {"key": "k8s.persistentvolumeclaim.name", "name": null, "displayName": "k8s.persistentvolumeclaim.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.persistentvolumeclaim.used", "displayName": "Kubernetes: PVC - used", "description": "This metric measures the number of used bytes in the volume. The most detailed level of aggregation is persistent volume claim.", "unit": "Byte", "entityType": ["CLOUD_APPLICATION_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.persistent_volume_claim.name", "name": null, "displayName": "k8s.persistent_volume_claim.name", "index": null, "type": "STRING"}, {"key": "k8s.persistentvolumeclaim.name", "name": null, "displayName": "k8s.persistentvolumeclaim.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.resourcequota.pods", "displayName": "Kubernetes: Resource quota - pod count", "description": "This metric measures the pods quota. The most detailed level of aggregation is resource quota. The value corresponds to the pods of a resource quota.", "unit": "Count", "entityType": ["CLOUD_APPLICATION_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.resourcequota.name", "name": null, "displayName": "k8s.resourcequota.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.resourcequota.pods_used", "displayName": "Kubernetes: Resource quota - pod used count", "description": "This metric measures the used pods quota. The most detailed level of aggregation is resource quota. The value corresponds to the used pods of a resource quota.", "unit": "Count", "entityType": ["CLOUD_APPLICATION_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.resourcequota.name", "name": null, "displayName": "k8s.resourcequota.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.conditions", "displayName": "Kubernetes: Workload conditions", "description": "This metric describes the status of a Kubernetes workload. The most detailed level of aggregation is workload.", "unit": "Count", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "condition_reason", "name": null, "displayName": "condition_reason", "index": null, "type": "STRING"}, {"key": "condition_status", "name": null, "displayName": "condition_status", "index": null, "type": "STRING"}, {"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}, {"key": "workload_condition", "name": null, "displayName": "workload_condition", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.containers_desired", "displayName": "Kubernetes: Pod - desired container count", "description": "This metric measures the number of desired containers. The most detailed level of aggregation is workload. The value is the count of all containers in the pod's specification.", "unit": "Count", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.cpu_usage", "displayName": "Kubernetes: Container - CPU usage (by workload)", "description": "This metric measures the total CPU consumed (user usage + system usage) by container. The most detailed level of aggregation is workload.", "unit": "MilliCores", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.limits_cpu", "displayName": "Kubernetes: Pod - CPU limits (by workload)", "description": "This metric measures the cpu limits. The most detailed level of aggregation is workload. The value is the sum of the cpu limits of all app containers of a pod.", "unit": "MilliCores", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.limits_memory", "displayName": "Kubernetes: Pod - memory limits (by workload)", "description": "This metric measures the memory limits. The most detailed level of aggregation is workload. The value is the sum of the memory limits of all app containers of a pod.", "unit": "Byte", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.memory_working_set", "displayName": "Kubernetes: Container - Working set memory (by workload)", "description": "This metric measures the current working set memory (memory that cannot be reclaimed under pressure) by container. The OOM Killer is invoked if the working set exceeds the limit. The most detailed level of aggregation is workload.", "unit": "Byte", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.pods_desired", "displayName": "Kubernetes: Workload - desired pod count", "description": "This metric measures the number of desired pods. The most detailed level of aggregation is workload. The value corresponds to the 'replicas' defined in a deployment resource and to the 'desiredNumberScheduled' for a daemon set resource's status as example.", "unit": "Count", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.requests_cpu", "displayName": "Kubernetes: Pod - CPU requests (by workload)", "description": "This metric measures the cpu requests. The most detailed level of aggregation is workload. The value is the sum of the cpu requests of all app containers of a pod.", "unit": "MilliCores", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workload.requests_memory", "displayName": "Kubernetes: Pod - memory requests (by workload)", "description": "This metric measures the memory requests. The most detailed level of aggregation is workload. The value is the sum of the memory requests of all app containers of a pod.", "unit": "Byte", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.containers", "displayName": "Kubernetes: Container count", "description": "This metric measures the number of containers. The most detailed level of aggregation is workload. The metric counts the number of all containers.", "unit": "Count", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "container_state", "name": null, "displayName": "container_state", "index": null, "type": "STRING"}, {"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.events", "displayName": "Kubernetes: Event count", "description": "This metric counts Kubernetes events. The most detailed level of aggregation is the event reason. The value corresponds to the count of events returned by the Kubernetes events endpoint. This metric depends on Kubernetes event monitoring. It will not show any datapoints for the period in which event monitoring is deactivated.", "unit": "Count", "entityType": ["KUBERNETES_CLUSTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_node", "name": null, "displayName": "Kubernetes node", "index": null, "type": "ENTITY"}, {"key": "dt.kubernetes.node.system_uuid", "name": null, "displayName": "dt.kubernetes.node.system_uuid", "index": null, "type": "STRING"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.event.involved_object.kind", "name": null, "displayName": "k8s.event.involved_object.kind", "index": null, "type": "STRING"}, {"key": "k8s.event.reason", "name": null, "displayName": "k8s.event.reason", "index": null, "type": "STRING"}, {"key": "k8s.event.type", "name": null, "displayName": "k8s.event.type", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.node.name", "name": null, "displayName": "k8s.node.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.nodes", "displayName": "Kubernetes: Node count", "description": "This metric measures the number of nodes. The most detailed level of aggregation is cluster. The value is the count of all nodes.", "unit": "Count", "entityType": ["KUBERNETES_CLUSTER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "node_condition_disk_pressure", "name": null, "displayName": "node_condition_disk_pressure", "index": null, "type": "STRING"}, {"key": "node_condition_memory_pressure", "name": null, "displayName": "node_condition_memory_pressure", "index": null, "type": "STRING"}, {"key": "node_condition_pid_pressure", "name": null, "displayName": "node_condition_pid_pressure", "index": null, "type": "STRING"}, {"key": "node_condition_ready", "name": null, "displayName": "node_condition_ready", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.pods", "displayName": "Kubernetes: Pod count (by workload)", "description": "This metric measures the number of pods. The most detailed level of aggregation is workload. The value corresponds to the count of all pods.", "unit": "Count", "entityType": ["CLOUD_APPLICATION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application", "name": null, "displayName": "Kubernetes workload", "index": null, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.cronjob.name", "name": null, "displayName": "k8s.cronjob.name", "index": null, "type": "STRING"}, {"key": "k8s.daemonset.name", "name": null, "displayName": "k8s.daemonset.name", "index": null, "type": "STRING"}, {"key": "k8s.deployment.name", "name": null, "displayName": "k8s.deployment.name", "index": null, "type": "STRING"}, {"key": "k8s.job.name", "name": null, "displayName": "k8s.job.name", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.pod.name", "name": null, "displayName": "k8s.pod.name", "index": null, "type": "STRING"}, {"key": "k8s.replicaset.name", "name": null, "displayName": "k8s.replicaset.name", "index": null, "type": "STRING"}, {"key": "k8s.statefulset.name", "name": null, "displayName": "k8s.statefulset.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}, {"key": "k8s.workload.name", "name": null, "displayName": "k8s.workload.name", "index": null, "type": "STRING"}, {"key": "pod_condition", "name": null, "displayName": "pod_condition", "index": null, "type": "STRING"}, {"key": "pod_phase", "name": null, "displayName": "pod_phase", "index": null, "type": "STRING"}, {"key": "pod_status", "name": null, "displayName": "pod_status", "index": null, "type": "STRING"}, {"key": "pod_status_reason", "name": null, "displayName": "pod_status_reason", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:kubernetes.workloads", "displayName": "Kubernetes: Workload count", "description": "This metric measures the number of workloads. The most detailed level of aggregation is namespace. The value corresponds to the count of all workloads.", "unit": "Count", "entityType": ["CLOUD_APPLICATION_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.name", "name": null, "displayName": "k8s.cluster.name", "index": null, "type": "STRING"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}, {"key": "k8s.workload.kind", "name": null, "displayName": "k8s.workload.kind", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:pgi.availability", "displayName": "Process availability %", "description": "This metric provides the percentage of time when a process is available. It is sent once per minute with a 10-second granularity - six samples are aggregated every minute. If the process is available for a whole minute, the value is 100%. A 0% value indicates that it is not running. It has a \"Process\" dimension (dt.entity.process_group_instance).", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:pgi.availability.state", "displayName": "Process availability", "description": "Process availability state metric reported in 1 minute intervals", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "availability.state", "name": null, "displayName": "availability.state", "index": null, "type": "STRING"}, {"key": "dt.entity.container_group", "name": null, "displayName": "Container group", "index": null, "type": "ENTITY"}, {"key": "dt.entity.container_group_instance", "name": null, "displayName": "Container group instance", "index": null, "type": "ENTITY"}, {"key": "dt.entity.host", "name": null, "displayName": "Host", "index": null, "type": "ENTITY"}, {"key": "dt.entity.process_group", "name": null, "displayName": "Process Group", "index": null, "type": "ENTITY"}, {"key": "dt.entity.process_group_instance", "name": null, "displayName": "Process", "index": null, "type": "ENTITY"}, {"key": "dt.metrics.source", "name": null, "displayName": "dt.metrics.source", "index": null, "type": "STRING"}, {"key": "host.name", "name": null, "displayName": "host.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:queue.incoming_requests", "displayName": "Incoming messages", "description": "The number of incoming messages on the queue or topic", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.queue", "name": null, "displayName": "Queue", "index": null, "type": "ENTITY"}, {"key": "dt.entity.service", "name": null, "displayName": "Service", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:queue.outgoing_requests", "displayName": "Outgoing messages", "description": "The number of outgoing messages from the queue or topic", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.queue", "name": null, "displayName": "Queue", "index": null, "type": "ENTITY"}, {"key": "dt.entity.service", "name": null, "displayName": "Service", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:security.vulnerabilities.global.countAffectedProcessGroups.notMuted", "displayName": "Vulnerabilities - affected not-muted process groups count (global)", "description": "Total number of unique affected process groups across all open, unmuted vulnerabilities per technology. The metric value is independent of any configured management zone (and thus global).", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "Technology", "name": null, "displayName": "Technology", "index": null, "type": "STRING"}, {"key": "Type", "name": null, "displayName": "Type", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:service.cpu.group.perRequest", "displayName": "CPU time", "description": "CPU time consumed by a key request within a particular request type. Request types classify requests, e.g. Resource requests for static assets like CSS or JS files. To learn how Dynatrace calculates service timings, see [Service analysis timings](https://dt-url.net/service-timings).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.cpu.group.time", "displayName": "Key request CPU time", "description": "CPU time consumed by a request type. Request types classify requests, e.g. Resource requests for static assets like CSS or JS files. To learn how Dynatrace calculates service timings, see [Service analysis timings](https://dt-url.net/service-timings).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.cpu.perRequest", "displayName": "CPU time", "description": "CPU time consumed by a particular request. To learn how Dynatrace calculates service timings, see [Service analysis timings](https://dt-url.net/service-timings).", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.cpu.time", "displayName": "Service CPU time", "description": "CPU time consumed by a particular service. To learn how Dynatrace calculates service timings, see [Service analysis timings](https://dt-url.net/service-timings).", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.dbconnections.failure", "displayName": "Failed connections", "description": "Unsuccessful connection attempts compared to all connection attempts. To learn about database analysis, see [Analyze database services](https://dt-url.net/database-services).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.dbconnections.failureRate", "displayName": "Connection failure rate", "description": "Rate of unsuccessful connection attempts compared to all connection attempts. To learn about database analysis, see [Analyze database services](https://dt-url.net/database-services).", "unit": "Percent", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.dbconnections.success", "displayName": "Successful connections", "description": "Total number of database connections successfully established by this service. To learn about database analysis, see [Analyze database services](https://dt-url.net/database-services).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.dbconnections.successRate", "displayName": "Connection success rate", "description": "Rate of successful connection attempts compared to all connection attempts. To learn about database analysis, see [Analyze database services](https://dt-url.net/database-services).", "unit": "Percent", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.dbconnections.total", "displayName": "Total number of connections", "description": "Total number of database connections that were attempted to be established by this service. To learn about database analysis, see [Analyze database services](https://dt-url.net/database-services).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.client.count", "displayName": "Number of client side errors", "description": "Failed requests for a service measured on client side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.client.rate", "displayName": "Failure rate (client side  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.client.successCount", "displayName": "Number of calls without  client side errors", "description": "", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.fivexx.count", "displayName": "Number of HTTP 5xx errors", "description": "HTTP requests with a status code between 500 and 599 for a given key request measured on server side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.fivexx.rate", "displayName": "Failure rate (HTTP 5xx  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.fivexx.successCount", "displayName": "Number of calls without  HTTP 5xx errors", "description": "", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.fourxx.count", "displayName": "Number of HTTP 4xx errors", "description": "HTTP requests with a status code between 400 and 499 for a given key request measured on server side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.fourxx.rate", "displayName": "Failure rate (HTTP 4xx  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.fourxx.successCount", "displayName": "Number of calls without  HTTP 4xx errors", "description": "", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.client.count", "displayName": "Number of client side errors", "description": "Failed requests for a given request type like dynamic web requests or static web requests measured on client side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.client.rate", "displayName": "Failure rate (client side  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.client.successCount", "displayName": "Number of calls without  client side errors", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.server.count", "displayName": "Number of server side errors", "description": "Failed requests for a given request type like dynamic web requests or static web requests measured on server side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.server.rate", "displayName": "Failure rate (server side  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.server.successCount", "displayName": "Number of calls without  server side errors", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.total.count", "displayName": "Number of any errors", "description": "Failed requests rate for a given request type like dynamic web requests or static web requests. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.total.rate", "displayName": "Failure rate (any  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.group.total.successCount", "displayName": "Number of calls without  any errors", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.server.count", "displayName": "Number of server side errors", "description": "Failed requests for a service measured on server side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.server.rate", "displayName": "Failure rate (server side  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.server.successCount", "displayName": "Number of calls without  server side errors", "description": "", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.total.count", "displayName": "Number of any errors", "description": "Failed requests for a service measured on server side or client side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.total.rate", "displayName": "Failure rate (any  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.errors.total.successCount", "displayName": "Number of calls without  any errors", "description": "", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.count.client", "displayName": "Request count - client", "description": "Number of requests for a given key request - measured on the client side. This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.count.server", "displayName": "Request count - server", "description": "Number of requests for a given key request - measured on the server side. This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.count.total", "displayName": "Request count", "description": "Number of requests for a given key request. This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.cpu.perRequest", "displayName": "CPU per request", "description": "CPU time for a given key request.  This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.cpu.time", "displayName": "Service key request CPU time", "description": "CPU time for a given key request.  This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.client.count", "displayName": "Number of client side errors", "description": "Failed requests for a given key request measured on client side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.client.rate", "displayName": "Failure rate (client side  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.client.successCount", "displayName": "Number of calls without  client side errors", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.fivexx.count", "displayName": "Number of HTTP 5xx errors", "description": "Rate of HTTP requests with a status code between 500 and 599 of a given key request. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.fivexx.rate", "displayName": "Failure rate (HTTP 5xx  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.fivexx.successCount", "displayName": "Number of calls without  HTTP 5xx errors", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.fourxx.count", "displayName": "Number of HTTP 4xx errors", "description": "Rate of HTTP requests with a status code between 400 and 499 of a given key request. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.fourxx.rate", "displayName": "Failure rate (HTTP 4xx  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.fourxx.successCount", "displayName": "Number of calls without  HTTP 4xx errors", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.server.count", "displayName": "Number of server side errors", "description": "Failed requests for a given key request measured on server side. To learn about failure detection, see [Configure service failure detection](https://dt-url.net/service-failuredetection).", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.server.rate", "displayName": "Failure rate (server side  errors)", "description": "", "unit": "Percent", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.errors.server.successCount", "displayName": "Number of calls without  server side errors", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.response.client", "displayName": "Client side response time", "description": "Response time for a given key request - measured on the client side.  This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.response.server", "displayName": "Server side response time", "description": "Response time for a given key request - measured on the server side.  This metric is written for each request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.response.time", "displayName": "Key request response time", "description": "Response time for a given key request.  This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.successes.server.rate", "displayName": "Success rate (server side)", "description": "", "unit": "Percent", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.dbChildCallCount", "displayName": "Number of calls to databases", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.dbChildCallTime", "displayName": "Time spent in database calls", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.ioTime", "displayName": "IO time", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.lockTime", "displayName": "Lock time", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.nonDbChildCallCount", "displayName": "Number of calls to other services", "description": "", "unit": "Count", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.nonDbChildCallTime", "displayName": "Time spent in calls to other services", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.totalProcessingTime", "displayName": "Total processing time", "description": "Total processing time for a given key request. This time includes potential further asynchronous processing. This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.keyRequest.waitTime", "displayName": "Wait time", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service_method", "name": "Service key request", "displayName": "Request", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.requestCount.client", "displayName": "Request count - client", "description": "Number of requests received by a given service - measured on the client side. This metric allows service splittings. To learn how Dynatrace detects and analyzes services, see [Services](https://dt-url.net/am-services).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.requestCount.server", "displayName": "Request count - server", "description": "Number of requests received by a given service - measured on the server side. This metric allows service splittings. To learn how Dynatrace detects and analyzes services, see [Services](https://dt-url.net/am-services).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.requestCount.total", "displayName": "Request count", "description": "Number of requests received by a given service. This metric allows service splittings. To learn how Dynatrace detects and analyzes services, see [Services](https://dt-url.net/am-services).", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.response.group.client", "displayName": "Client side response time", "description": "Response time for a given key request per request type - measured on the client side.  This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.response.group.server", "displayName": "Server side response time", "description": "Response time for a given key request per request type - measured on the server side. This metric is written for each key request. To learn more about key requests, see [Monitor key request](https://dt-url.net/key-request).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.response.client", "displayName": "Client side response time", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.response.server", "displayName": "Server side response time", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.response.time", "displayName": "Response time", "description": "Time consumed by a particular service until a response is sent back to the calling application, process, service etc.To learn how Dynatrace calculates service timings, see [Service analysis timings](https://dt-url.net/service-timings).", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.successes.server.rate", "displayName": "Success rate (server side)", "description": "", "unit": "Percent", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.totalProcessingTime", "displayName": "Total processing time", "description": "Total time consumed by a particular service including asynchronous processing. Time includes the factor that asynchronous processing can still occur after responses are sent.To learn how Dynatrace calculates service timings, see [Service analysis timings](https://dt-url.net/service-timings).", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.totalProcessingTime.group.totalProcessingTime", "displayName": "Total processing time", "description": "Total time consumed by a particular request type including asynchronous processing. Time includes the factor that asynchronous processing can still occur after responses are sent. To learn how Dynatrace calculates service timings, see [Service analysis timings](https://dt-url.net/service-timings).", "unit": "MicroSecond", "entityType": ["SERVICE_METHOD_GROUP"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.service_method_group", "name": "Service key request group", "displayName": "Request type", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.dbChildCallCount", "displayName": "Number of calls to databases", "description": "", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.dbChildCallTime", "displayName": "Time spent in database calls", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.ioTime", "displayName": "IO time", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.lockTime", "displayName": "Lock time", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.nonDbChildCallCount", "displayName": "Number of calls to other services", "description": "", "unit": "Count", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.nonDbChildCallTime", "displayName": "Time spent in calls to other services", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:service.waitTime", "displayName": "Wait time", "description": "", "unit": "MicroSecond", "entityType": ["SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.service", "name": "Service", "displayName": "Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:synthetic.browser.availability.location.total", "displayName": "Availability rate (by location) [browser monitor]", "description": "The availability rate of browser monitors.", "unit": "Percent", "entityType": ["SYNTHETIC_TEST"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.synthetic_test", "name": "Synthetic monitor", "displayName": "Synthetic monitor", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:synthetic.browser.availability.location.totalWoMaintenanceWindow", "displayName": "Availability rate - excl. maintenance windows (by location) [browser monitor]", "description": "The availability rate of browser monitors excluding maintenance windows.", "unit": "Percent", "entityType": ["SYNTHETIC_TEST"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.synthetic_test", "name": "Synthetic monitor", "displayName": "Synthetic monitor", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:synthetic.external.availability.location.total", "displayName": "Availability rate (by location) [third-party monitor]", "description": "The availability rate of third-party monitors.", "unit": "Percent", "entityType": ["EXTERNAL_SYNTHETIC_TEST"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.external_synthetic_test", "name": "Synthetic monitor", "displayName": "Third-party synthetic test", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.synthetic_location", "name": "Location", "displayName": "Synthetic Location", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:synthetic.external.availability.location.totalWoMaintenanceWindow", "displayName": "Availability rate - excl. maintenance windows (by location) [third-party monitor]", "description": "The availability rate of third-party monitors excluding maintenance windows.", "unit": "Percent", "entityType": ["EXTERNAL_SYNTHETIC_TEST"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.external_synthetic_test", "name": "Synthetic monitor", "displayName": "Third-party synthetic test", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.synthetic_location", "name": "Location", "displayName": "Synthetic Location", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:synthetic.http.availability.location.total", "displayName": "Availability rate (by location) [HTTP monitor]", "description": "The availability rate of HTTP monitors.", "unit": "Percent", "entityType": ["HTTP_CHECK"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.http_check", "name": "Synthetic monitor", "displayName": "HTTP monitor", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.synthetic_location", "name": "Location", "displayName": "Synthetic Location", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:synthetic.http.availability.location.totalWoMaintenanceWindow", "displayName": "Availability rate - excl. maintenance windows (by location) [HTTP monitor]", "description": "The availability rate of HTTP monitors excluding maintenance windows.", "unit": "Percent", "entityType": ["HTTP_CHECK"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.http_check", "name": "Synthetic monitor", "displayName": "HTTP monitor", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.synthetic_location", "name": "Location", "displayName": "Synthetic Location", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.cpu.groupSuspensionTime", "displayName": "Process group total CPU time during GC suspensions", "description": "This metric provides statistics about CPU usage for process groups of garbage-collected technologies. The metric value is the sum of CPU time used during garbage collector suspensions for every process (including its workers) in a process group. It has a \"Process Group\" dimension.", "unit": "MicroSecond", "entityType": ["PROCESS_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group", "name": "Process group", "displayName": "Process Group", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.cpu.groupTotalTime", "displayName": "Process group total CPU time", "description": "This metric provides the total CPU time used by a process group. The metric value is the sum of CPU time every process (including its workers) of the process group uses. The result is expressed in microseconds. It can help to identify the most CPU-intensive technologies in the monitored environment. It has a \"Process Group\" dimension.", "unit": "MicroSecond", "entityType": ["PROCESS_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group", "name": "Process group", "displayName": "Process Group", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.cpu.suspensionTime", "displayName": "Process total CPU time during GC suspensions", "description": "This metric provides statistics about CPU usage for garbage-collected processes. The metric value is the sum of CPU time used during garbage collector suspensions for all process workers. It has a \"Process\" dimension (dt.entity.process_group_instance).", "unit": "MicroSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.cpu.totalTime", "displayName": "Process total CPU time", "description": "This metric provides the CPU time used by a process. The metric value is the sum of CPU time every process worker uses. The result is expressed in microseconds. It has a \"Process\" dimension (dt.entity.process_group_instance).", "unit": "MicroSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.cpu.usage", "displayName": "Process CPU usage", "description": "This metric is the percentage of the CPU usage of a process. The metric value is the sum of CPU time every process worker uses divided by the total available CPU time. The result is expressed in percentage. A value of 100% indicates that the process uses all available CPU resources of the host.", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.handles.fileDescriptorsMax", "displayName": "Process file descriptors max", "description": "This metric provides statistics about the file descriptor resource limits. It is supported on Linux. The metric value is the total limit of file descriptors that all process workers can open. It is sent once per minute with a 10-second granularity - (six samples are aggregated every minute).", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.handles.fileDescriptorsPercentUsed", "displayName": "Process file descriptors used per PID", "description": "This metric provides the file descriptor usage statistics. It is supported on Linux. The metric value is the highest percentage of the currently used file descriptor limit among process workers. It is sent once per minute with a 10-second granularity - (six samples are aggregated every minute). It offers two dimensions: \"Process\" (`dt.entity.process_group_instance`) and pid dimension corresponding to the PID with the highest percentage of available descriptors usage.", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.generic.handles.fileDescriptorsUsed", "displayName": "Process file descriptors used", "description": "This metric provides statistics about file descriptor usage. It is supported on Linux. The metric value is the total number of file descriptors all process workers have opened. You can use it to detect processes that may cause the system to reach the limit of open file descriptors.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.io.bytesRead", "displayName": "Process I/O read bytes", "description": "This metric provides statistics about the I/O read operations of a process. The metric value is a sum of I/O bytes read from the storage layer by all process workers per second. High values help to identify bottlenecks reducing process performance caused by the slow read speed of the storage device.", "unit": "BytePerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.io.bytesTotal", "displayName": "Process I/O bytes total", "description": "This metric provides statistics about I/O operations for a process. The metric value is a sum of I/O bytes read and written by all process workers per second.", "unit": "BytePerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": null, "displayName": "Process", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.io.bytesWritten", "displayName": "Process I/O write bytes", "description": "This metric provides statistics about the I/O write operations of a process. The metric value is a sum of I/O bytes written to the storage layer by all process workers per second. High values help to identify bottlenecks reducing process performance caused by the slow write speed of the storage device.", "unit": "BytePerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.io.reqBytesRead", "displayName": "Process I/O requested read bytes", "description": "This metric provides statistics about the I/O read operations a process requests. It is supported only on Linux and AIX. The metric value is a sum of I/O bytes requested to be read from the storage by worker processes per second. It includes additional read operations, such as terminal I/O. It does not indicate the actual disk I/O operations, as some parts of the read operation might have been satisfied from the page cache.", "unit": "BytePerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.io.reqBytesWrite", "displayName": "Process I/O requested write bytes", "description": "This metric provides statistics about the I/O write operations a process requests. It is supported on Linux and AIX. The metric value is a sum of I/O bytes requested to be written to the storage by PGI processes per second. It includes additional write operations, such as terminal I/O. It does not indicate the actual disk I/O operations, as some parts of the write operation might have been satisfied from the page cache.", "unit": "BytePerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.mem.pageFaults", "displayName": "Process page faults counter", "description": "This metric is the rate of page faults for a process. The metric value is the sum of page faults per time unit of every process worker. A page fault occurs when the process attempts to access a memory block not stored in the RAM, which means that the block has to be identified in the virtual memory and then loaded from the storage. Lower values are better. A high number of page faults may indicate reduced performance due to insufficient memory size.", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.mem.usage", "displayName": "Process memory usage", "description": "This metric is the percentage of memory used by a process. It helps to identify processes with high memory resource consumption and memory leaks. The metric value is the sum of the memory used by every process worker divided by the total available memory in the host.", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.mem.workingSetSize", "displayName": "Process memory", "description": "This metric is the memory usage of a process. It helps to identify processes with high memory resource consumption and memory leaks. The metric value represents the sum of every process worker's used memory size (including shared memory).", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.baseReRx", "displayName": "Retransmission base received", "description": "Number of retransmitted packets base received per second on host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.baseReRxAggr", "displayName": "Retransmission base received", "description": "Number of retransmitted packets base received per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.baseReTx", "displayName": "Retransmission base sent", "description": "Number of retransmitted packets base sent per second on host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.baseReTxAggr", "displayName": "Retransmission base sent", "description": "Number of retransmitted packets base sent per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.reRx", "displayName": "Retransmitted packets received", "description": "Number of retransmitted packets received per second on host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.reRxAggr", "displayName": "Retransmitted packets received", "description": "Number of retransmitted packets received per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.reTx", "displayName": "Retransmitted packets sent", "description": "Number of retransmitted packets base sent per second on host", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.reTxAggr", "displayName": "Retransmitted packets", "description": "Number of retransmitted packets sent per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.retransmission", "displayName": "Packet retransmissions", "description": "Packet retransmissions percent", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.retransmissionIn", "displayName": "Incoming packet retransmissions", "description": "Incoming packet retransmissions percent", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": null, "displayName": "Process", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.retransmissionOut", "displayName": "Outgoing packet retransmissions", "description": "Outgoing packet retransmissions percent", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": null, "displayName": "Process", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.rx", "displayName": "Packets received", "description": "Number of packets received per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.packets.tx", "displayName": "Packets sent", "description": "Number of packets sent per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.connectivity", "displayName": "TCP connectivity", "description": "Percentage of successfully established TCP sessions", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": null, "displayName": "Process", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.new", "displayName": "New session received", "description": "Number of new incoming TCP sessions per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.newAggr", "displayName": "New session received", "description": "Number of new sessions received per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.newLocal", "displayName": "New session received", "description": "Number of new sessions received per second on localhost", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.reset", "displayName": "Session reset received", "description": "Number of incoming TCP sessions with reset error per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.resetAggr", "displayName": "Session reset received", "description": "Number of session resets received per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.resetLocal", "displayName": "Session reset received", "description": "Number of session resets received per second on localhost", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.timeout", "displayName": "Session timeout received", "description": "Number of incoming TCP sessions with timeout error per second", "unit": "PerSecond", "entityType": ["HOST"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.network_interface", "name": "Network interface", "displayName": "Network Interface", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.timeoutAggr", "displayName": "Session timeout received", "description": "Number of session timeouts received per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.sessions.timeoutLocal", "displayName": "Session timeout received", "description": "Number of session timeouts received per second on localhost", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.traffic.traffic", "displayName": "Traffic", "description": "Summary of incoming and outgoing network traffic", "unit": "BitPerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": null, "displayName": "Process", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.traffic.trafficIn", "displayName": "Traffic in", "description": "Incoming network traffic at PGI", "unit": "BitPerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.traffic.trafficOut", "displayName": "Traffic out", "description": "Outgoing network traffic from PGI", "unit": "BitPerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.bytesRx", "displayName": "Bytes received", "description": "Number of bytes received per second", "unit": "BytePerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.bytesTx", "displayName": "Bytes sent", "description": "Number of bytes sent per second", "unit": "BytePerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.latency", "displayName": "Ack-round-trip time", "description": "Average latency between outgoing TCP data and ACK", "unit": "MilliSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.load", "displayName": "Requests", "description": "Number of requests per second", "unit": "PerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.responsiveness", "displayName": "Server responsiveness", "description": "Server responsiveness in microseconds", "unit": "MicroSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.roundTrip", "displayName": "Round-trip time", "description": "Average TCP session handshake RTT", "unit": "MilliSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.network.throughput", "displayName": "Throughput", "description": "Used network bandwidth", "unit": "BytePerSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.count", "displayName": "Process count per process group", "description": "This metric provides the number of processes in a process group. It can tell how many instances of the technology are running in the monitored environment. It has a \"Process Group\" dimension.", "unit": "Count", "entityType": ["PROCESS_GROUP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group", "name": "Process group", "displayName": "Process Group", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.generic.processCount", "displayName": "Worker processes", "description": "This metric is the number of process workers. Too few worker processes may lead to performance degradation, while too many may waste available resources. Configuration of workers should be suitable for the average workload and be able to scale up with higher demand.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.go.http.badGateways", "displayName": "Go: 502 responses", "description": "The number of responses that indicate invalid service responses produced by an application.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.http.latency", "displayName": "Go: Response latency", "description": "The average response time from the application to clients.", "unit": "MilliSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.http.responses5xx", "displayName": "Go: 5xx responses", "description": "The number of responses that indicate repeatedly crashing apps or response issues from applications.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.http.totalRequests", "displayName": "Go: Total requests", "description": "The number of all requests representing the overall traffic flow.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.memory.heap.idle", "displayName": "Go: Heap idle size", "description": "The amount of memory not assigned to the heap or stack. Idle memory can be returned to the operating system or retained by the Go runtime for later reassignment to the heap or stack.", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.memory.heap.live", "displayName": "Go: Heap live size", "description": "The amount of memory considered live by the Go garbage collector. This metric accumulates memory retained by the most recent garbage collector run and allocated since then.", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.memory.heap.objCount", "displayName": "Go: <PERSON><PERSON> allocated Go objects count", "description": "The number of Go objects allocated on the Go heap.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.memory.pool.committed", "displayName": "Go: Committed memory", "description": "The amount of memory committed to the Go runtime heap.", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}, {"key": "poolname", "name": "poolname", "displayName": "poolname", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.go.memory.pool.used", "displayName": "Go: Used memory", "description": "The amount of memory used by the Go runtime heap.", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}, {"key": "poolname", "name": "poolname", "displayName": "poolname", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.go.memory.gcCount", "displayName": "Go: Garbage collector invocation count", "description": "The number of Go garbage collector runs.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.native.cgoCalls", "displayName": "Go: Go to C language (cgo) call count", "description": "The number of Go to C language (cgo) calls.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.native.sysCalls", "displayName": "Go: Go runtime system call count", "description": "The number of system calls executed by the Go runtime. This number doesn't include system calls performed by user code.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.scheduling.g.runningCount", "displayName": "Go: Application Goroutine count", "description": "The number of Goroutines instantiated by the user application.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.scheduling.g.systemCount", "displayName": "Go: System Goroutine count", "description": "The number of Goroutines instantiated by the Go runtime.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.scheduling.m.count", "displayName": "Go: Worker thread count", "description": "The number of operating system threads instantiated to execute Goroutines. Go doesn't terminate worker threads; it keeps them in a parked state for future reuse.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.scheduling.m.idlingCount", "displayName": "Go: Parked worker thread count", "description": "The number of worker threads parked by Go runtime. A parked worker thread doesn't consume CPU cycles until the Go runtime unparks the thread.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.scheduling.m.spinningCount", "displayName": "Go: Out-of-work worker thread count", "description": "The number of worker threads whose associated scheduling context has no more Goroutines to execute. When this happens, the worker thread attempts to steal Goroutines from another scheduling context or the global run queue. If the stealing fails, the worker thread parks itself after some time. This same mechanism applies to a high workload scenario. When an idle scheduling context exists, the Go runtime unparks a parked worker thread and associates it with the idle scheduling context. The unparked worker thread is now in the 'out of work' state and starts Goroutine stealing.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.scheduling.p.idleCount", "displayName": "Go: Idle scheduling context count", "description": "The number of scheduling contexts that have no more Goroutines to execute and for which Goroutine acquisition from the global run queue or other scheduling contexts has failed.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.go.scheduling.globalQSize", "displayName": "Go: Global Goroutine run queue size", "description": "The number of Goroutines in the global run queue. Goroutines are placed in the global run queue if the worker thread used to execute a blocking system call can't acquire a scheduling context. Scheduling contexts periodically acquire Goroutines from the global run queue.", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.jvm.classes.loaded", "displayName": "JVM loaded classes", "description": "The number of classes that are currently loaded in the Java virtual machine, https://dt-url.net/l2c34jw", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.classes.total", "displayName": "JVM total number of loaded classes", "description": "The total number of classes that have been loaded since the Java virtual machine has started execution, https://dt-url.net/d0y347x", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.classes.unloaded", "displayName": "JVM unloaded classes", "description": "The total number of classes unloaded since the Java virtual machine has started execution, https://dt-url.net/d7g34bi", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.gc.activationCount", "displayName": "Garbage collection total activation count", "description": "The total number of collections that have occurred for all pools, https://dt-url.net/oz834vd", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.gc.collectionTime", "displayName": "Garbage collection total collection time", "description": "The approximate accumulated collection elapsed time in milliseconds for all pools, https://dt-url.net/oz834vd", "unit": "MilliSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.gc.suspensionTime", "displayName": "Garbage collection suspension time", "description": "Time spent in milliseconds between GC pause starts and GC pause ends, https://dt-url.net/zj434js", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.pool.collectionCount", "displayName": "Garbage collection count", "description": "The total number of collections that have occurred in that pool, https://dt-url.net/z9034yg", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}, {"key": "poolname", "name": "poolname", "displayName": "poolname", "index": 2, "type": "STRING"}, {"key": "gcname", "name": "gcname", "displayName": "gcname", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.pool.collectionTime", "displayName": "Garbage collection time", "description": "The approximate accumulated collection elapsed time in milliseconds in that pool, https://dt-url.net/z9034yg", "unit": "MilliSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}, {"key": "poolname", "name": "poolname", "displayName": "poolname", "index": 2, "type": "STRING"}, {"key": "gcname", "name": "gcname", "displayName": "gcname", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.pool.committed", "displayName": "JVM heap memory pool committed bytes", "description": "The amount of memory (in bytes) that is guaranteed to be available for use by the Java virtual machine, https://dt-url.net/1j034o0", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}, {"key": "poolname", "name": "poolname", "displayName": "poolname", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.pool.max", "displayName": "JVM heap memory max bytes", "description": "The maximum amount of memory (in bytes) that can be used for memory management, https://dt-url.net/1j034o0", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}, {"key": "poolname", "name": "poolname", "displayName": "poolname", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.pool.used", "displayName": "JVM heap memory pool used bytes", "description": "The amount of memory currently used by the memory pool (in bytes), https://dt-url.net/1j034o0", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}, {"key": "poolname", "name": "poolname", "displayName": "poolname", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.runtime.free", "displayName": "JVM runtime free memory", "description": "An approximation to the total amount of memory currently available for future allocated objects, measured in bytes, https://dt-url.net/2mm34yx", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.runtime.max", "displayName": "JVM runtime max memory", "description": "The maximum amount of memory that the virtual machine will attempt to use, measured in bytes, https://dt-url.net/lzq34mm", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.runtime.total", "displayName": "JVM runtime total memory", "description": "The total amount of memory currently available for current and future objects, measured in bytes, https://dt-url.net/otu34eo", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.memAllocationBytes", "displayName": "Process memory allocation bytes", "description": "", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "API", "name": "API", "displayName": "API", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.memAllocationCount", "displayName": "Process memory allocation objects count", "description": "", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "API", "name": "API", "displayName": "API", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.memSurvivorsBytes", "displayName": "Process memory survived objects bytes", "description": "", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "API", "name": "API", "displayName": "API", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.memory.memSurvivorsCount", "displayName": "Process memory survived objects count", "description": "", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "API", "name": "API", "displayName": "API", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.threads.avgActiveThreadCount", "displayName": "JVM average number of active threads", "description": "", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "API", "name": "API", "displayName": "API", "index": 1, "type": "STRING"}, {"key": "Thread state", "name": "Thread state", "displayName": "Thread state", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.threads.avgInactiveThreadCount", "displayName": "JVM average number of inactive threads", "description": "", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "API", "name": "API", "displayName": "API", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.jvm.threads.count", "displayName": "JVM thread count", "description": "The current number of live threads including both daemon and non-daemon threads, https://dt-url.net/s02346y", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.jvm.threads.totalCpuTime", "displayName": "JVM total CPU time", "description": "", "unit": "MilliSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "Thread group", "name": "Thread group", "displayName": "Thread group", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:tech.nodejs.uvLoop.activeHandles", "displayName": "Node.js: Active handles", "description": "Average number of active handles in the event loop", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.nodejs.uvLoop.count", "displayName": "Node.js: Event loop tick frequency", "description": "Average number of event loop iterations (per 10 seconds interval)", "unit": "Count", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.nodejs.uvLoop.loopLatency", "displayName": "Node.js: Event loop latency", "description": "Average latency of expected event completion", "unit": "NanoSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.nodejs.uvLoop.processedLatency", "displayName": "Node.js: Work processed latency", "description": "Average latency of a work item being enqueued and callback being called", "unit": "NanoSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.nodejs.uvLoop.totalTime", "displayName": "Node.js: Event loop tick duration", "description": "Average duration of an event loop iteration (tick)", "unit": "NanoSecond", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.nodejs.uvLoop.utilization", "displayName": "Node.js: Event loop utilization", "description": "Event loop utilization represents the percentage of time the event loop has been active", "unit": "Percent", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}, {"key": "rx_pid", "name": "rx_pid", "displayName": "rx_pid", "index": 1, "type": "NUMBER"}], "tags": []}, {"metricId": "builtin:tech.nodejs.v8heap.gcHeapUsed", "displayName": "Node.js: GC heap used", "description": "Total size of allocated V8 heap used by application data (post-GC memory snapshot)", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.nodejs.v8heap.rss", "displayName": "Node.js: Process Resident Set <PERSON> (RSS)", "description": "Amount of space occupied in the main memory", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:tech.nodejs.v8heap.total", "displayName": "Node.js: V8 heap total", "description": "Total size of allocated V8 heap", "unit": "Byte", "entityType": ["PROCESS_GROUP_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.process_group_instance", "name": "Process", "displayName": "Process", "index": 0, "type": "ENTITY"}], "tags": []}]}