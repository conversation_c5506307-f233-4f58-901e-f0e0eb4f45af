{"timestamp": "2025-06-02T12:51:56.065388", "total_metrics": 500, "metrics": [{"metricId": "builtin:apps.custom.reportedErrorCount", "displayName": "Reported error count (by OS, app version) [custom]", "description": "The number of all reported errors.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:apps.custom.sessionCount", "displayName": "Session count (by OS, app version) [custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:apps.mobile.sessionCount", "displayName": "Session count (by OS, app version, crash replay feature status) [mobile]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "App Version", "name": null, "displayName": "App Version", "index": null, "type": "STRING"}, {"key": "Crash replay feature status", "name": null, "displayName": "Crash replay feature status", "index": null, "type": "STRING"}, {"key": "dt.entity.mobile_application", "name": null, "displayName": "Mobile App", "index": null, "type": "ENTITY"}, {"key": "dt.entity.os", "name": null, "displayName": "OS", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.mobile.sessionCount.sessionReplayStatus", "displayName": "Session count (by OS, app version, full replay feature status) [mobile]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:apps.mobile.reportedErrorCount", "displayName": "Reported error count (by OS, app version) [mobile]", "description": "The number of all reported errors.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:apps.other.apdex.osAndGeo", "displayName": "Apdex (by OS, geolocation) [mobile, custom]", "description": "The Apdex rating for all captured user actions.", "unit": "Unspecified", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 2, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.apdex.osAndVersion", "displayName": "Apdex (by OS, app version) [mobile, custom]", "description": "The Apdex rating for all captured user actions.", "unit": "Unspecified", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.crashAffectedUsers.os", "displayName": "User count - estimated users affected by crashes (by OS) [mobile, custom]", "description": "The estimated number of unique users affected by a crash. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.crashAffectedUsers.osAndVersion-std", "displayName": "User count - estimated users affected by crashes (by OS, app version) [mobile, custom]", "description": "The estimated number of unique users affected by a crash. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.crashAffectedUsersRate.os", "displayName": "User rate - estimated users affected by crashes (by OS) [mobile, custom]", "description": "The estimated percentage of unique users affected by a crash. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Percent", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.crashCount.osAndGeo", "displayName": "Crash count (by OS, geolocation) [mobile, custom]", "description": "The number of detected crashes.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 2, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.crashCount.osAndVersion", "displayName": "Crash count (by OS, app version) [mobile, custom]", "description": "The number of detected crashes.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Version", "name": "Version", "displayName": "Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.crashCount.osAndVersion-std", "displayName": "Crash count (by OS, app version) [mobile, custom]", "description": "The number of detected crashes.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.crashFreeUsersRate.os", "displayName": "User rate - estimated crash free users (by OS) [mobile, custom]", "description": "The estimated percentage of unique users not affected by a crash. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Percent", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.keyUserActions.apdexValue.os", "displayName": "Apdex (by key user action, OS) [mobile, custom]", "description": "The Apdex rating for all captured key user actions.", "unit": "Unspecified", "entityType": ["DEVICE_APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application_method", "name": "Application method", "displayName": "Mobile app key user action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.keyUserActions.count.osAndApdex", "displayName": "Action count (by key user action, OS, Apdex category) [mobile, custom]", "description": "The number of captured key user actions.", "unit": "Count", "entityType": ["DEVICE_APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application_method", "name": "Application method", "displayName": "Mobile app key user action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Apdex category", "name": "Apdex category", "displayName": "Apdex category", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.keyUserActions.duration.os", "displayName": "Action duration (by key user action, OS) [mobile, custom]", "description": "The duration of key user actions.", "unit": "MicroSecond", "entityType": ["DEVICE_APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.device_application_method", "name": "Application method", "displayName": "Mobile app key user action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.keyUserActions.reportedErrorCount.os", "displayName": "Reported error count (by key user action, OS) [mobile, custom]", "description": "The number of reported errors for key user actions.", "unit": "Count", "entityType": ["DEVICE_APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application_method", "name": "Application method", "displayName": "Mobile app key user action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.keyUserActions.requestCount.os", "displayName": "Request count (by key user action, OS) [mobile, custom]", "description": "The number of captured web requests associated with key user actions.", "unit": "Count", "entityType": ["DEVICE_APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application_method", "name": "Application method", "displayName": "Mobile app key user action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.keyUserActions.requestDuration.os", "displayName": "Request duration (by key user action, OS) [mobile, custom]", "description": "The duration of web requests for key user actions. Be aware that this metric is measured in microseconds while other request duration metrics for mobile and custom apps are measured in milliseconds.", "unit": "MilliSecond", "entityType": ["DEVICE_APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.device_application_method", "name": "Application method", "displayName": "Mobile app key user action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.keyUserActions.requestErrorCount.os", "displayName": "Request error count (by key user action, OS) [mobile, custom]", "description": "The number of detected web request errors for key user actions.", "unit": "Count", "entityType": ["DEVICE_APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application_method", "name": "Application method", "displayName": "Mobile app key user action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.keyUserActions.requestErrorRate.os", "displayName": "Request error rate (by key user action, OS) [mobile, custom]", "description": "The percentage of web requests with detected errors for key user actions", "unit": "Percent", "entityType": ["DEVICE_APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application_method", "name": "Application method", "displayName": "Mobile app key user action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.newUsers.os", "displayName": "New user count (by OS) [mobile, custom]", "description": "The number of users that launched the application(s) for the first time. The metric is tied to specific devices, so users are counted multiple times if they install the application on multiple devices. The metric doesn't distinguish between multiple users that share the same device and application installation.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.requestCount.osAndProvider", "displayName": "Request count (by OS, provider) [mobile, custom]", "description": "The number of captured web requests.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Provider Type", "name": "Provider Type", "displayName": "Provider Type", "index": 2, "type": "STRING"}, {"key": "Provider", "name": "Provider", "displayName": "Provider", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestCount.osAndVersion", "displayName": "Request count (by OS, app version) [mobile, custom]", "description": "The number of captured web requests.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestErrorCount.osAndProvider", "displayName": "Request error count (by OS, provider) [mobile, custom]", "description": "The number of detected web request errors.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Provider Type", "name": "Provider Type", "displayName": "Provider Type", "index": 2, "type": "STRING"}, {"key": "Provider", "name": "Provider", "displayName": "Provider", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestErrorCount.osAndVersion", "displayName": "Request error count (by OS, app version) [mobile, custom]", "description": "The number of detected web request errors.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestErrorRate.osAndProvider", "displayName": "Request error rate (by OS, provider) [mobile, custom]", "description": "The percentage of web requests with detected errors.", "unit": "Percent", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Provider Type", "name": "Provider Type", "displayName": "Provider Type", "index": 2, "type": "STRING"}, {"key": "Provider", "name": "Provider", "displayName": "Provider", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestErrorRate.osAndVersion", "displayName": "Request error rate (by OS, app version) [mobile, custom]", "description": "The percentage of web requests with detected errors.", "unit": "Percent", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestTimes.osAndProvider", "displayName": "Request duration (by OS, provider) [mobile, custom]", "description": "The duration of web requests.", "unit": "MilliSecond", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Provider Type", "name": "Provider Type", "displayName": "Provider Type", "index": 2, "type": "STRING"}, {"key": "Provider", "name": "Provider", "displayName": "Provider", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.requestTimes.osAndVersion", "displayName": "Request duration (by OS, app version) [mobile, custom]", "description": "The duration of web requests.", "unit": "MilliSecond", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.agentVersionAndOs", "displayName": "Session count (by agent version, OS) [mobile, custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "Agent Version", "name": "Agent Version", "displayName": "Agent Version", "index": 1, "type": "STRING"}, {"key": "Operating System", "name": "Operating System", "displayName": "Operating System", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.osAndCrashReportingLevel", "displayName": "Session count (by OS, crash reporting level) [mobile, custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Crash reporting level", "name": "Crash reporting level", "displayName": "Crash reporting level", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.osAndDataCollectionLevel", "displayName": "Session count (by OS, data collection level) [mobile, custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Data collection level", "name": "Data collection level", "displayName": "Data collection level", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.osAndGeo", "displayName": "Session count - estimated (by OS, geolocation) [mobile, custom]", "description": "The estimated number of captured user sessions. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 2, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.sessionCount.osAndVersion-std", "displayName": "Session count (by OS, app version) [mobile, custom]", "description": "The number of captured user sessions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.uaCount.geoAndApdex", "displayName": "Action count (by geolocation, Apdex category) [mobile, custom]", "description": "The number of captured user actions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "Apdex category", "name": "Apdex category", "displayName": "Apdex category", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.uaCount.osAndApdex", "displayName": "Action count (by OS, Apdex category) [mobile, custom]", "description": "The number of captured user actions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "Apdex category", "name": "Apdex category", "displayName": "Apdex category", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.uaCount.osAndVersion", "displayName": "Action count (by OS, app version) [mobile, custom]", "description": "The number of captured user actions.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.uaDuration.osAndVersion", "displayName": "Action duration (by OS, app version) [mobile, custom]", "description": "The duration of user actions.", "unit": "MicroSecond", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.other.userCount.osAndGeo", "displayName": "User count - estimated (by OS, geolocation) [mobile, custom]", "description": "The estimated number of unique users that have a mapped geolocation. The metric is based on 'internalUserId'. When 'dataCollectionLevel' is set to 'performance' or 'off', 'internalUserId' is changed at each app start. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 2, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.other.userCount.osAndVersion-std", "displayName": "User count - estimated (by OS, app version) [mobile, custom]", "description": "The estimated number of unique users. The metric is based on 'internalUserId'. When 'dataCollectionLevel' is set to 'performance' or 'off', 'internalUserId' is changed at each app start. For this high cardinality metric, the HyperLogLog algorithm is used to approximate the number of users.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.os", "name": "Operating system", "displayName": "OS", "index": 1, "type": "ENTITY"}, {"key": "App Version", "name": "App Version", "displayName": "App Version", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.affectedUas", "displayName": "User action rate - affected by JavaScript errors (by key user action, user type) [web]", "description": "The percentage of key user actions with detected JavaScript errors.", "unit": "Percent", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.apdex", "displayName": "Apdex (by key user action) [web]", "description": "The average Apdex rating for key user actions.", "unit": "Unspecified", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.count.custom.browser", "displayName": "Action count - custom action (by key user action, browser) [web]", "description": "The number of custom actions that are marked as key user actions.", "unit": "Count", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.count.load.browser", "displayName": "Action count - load action (by key user action, browser) [web]", "description": "The number of load actions that are marked as key user actions.", "unit": "Count", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.count.xhr.browser", "displayName": "Action count - XHR action (by key user action, browser) [web]", "description": "The number of XHR actions that are marked as key user actions.", "unit": "Count", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.cumulativeLayoutShift.load.userType", "displayName": "Cumulative Layout Shift - load action (by key user action, user type) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions that are marked as key user actions.", "unit": "Unspecified", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.cumulativeLayoutShift.load.userType.geo", "displayName": "Cumulative Layout Shift - load action (by key user action, geolocation, user type) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions that are marked as key user actions.", "unit": "Unspecified", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.cumulativeLayoutShift.load.browser", "displayName": "Cumulative Layout Shift - load action (by key user action, browser) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions that are marked as key user actions.", "unit": "Unspecified", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.domInteractive.load.browser", "displayName": "DOM interactive - load action (by key user action, browser) [web]", "description": "The time taken until a page's status is set to \"interactive\" and it's ready to receive user input. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.duration.custom.browser", "displayName": "Action duration - custom action (by key user action, browser) [web]", "description": "The duration of custom actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.duration.load.browser", "displayName": "Action duration - load action (by key user action, browser) [web]", "description": "The duration of load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.duration.xhr.browser", "displayName": "Action duration - XHR action (by key user action, browser) [web]", "description": "The duration of XHR actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.firstByte.load.browser", "displayName": "Time to first byte - load action (by key user action, browser) [web]", "description": "The time taken until the first byte of the response is received from the server, relevant application caches, or a local resource. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.firstByte.xhr.browser", "displayName": "Time to first byte - XHR action (by key user action, browser) [web]", "description": "The time taken until the first byte of the response is received from the server, relevant application caches, or a local resource. Calculated for XHR actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.firstInputDelay.load.userType", "displayName": "First Input Delay - load action (by key user action, user type) [web]", "description": "The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.firstInputDelay.load.userType.geo", "displayName": "First Input Delay - load action (by key user action, geolocation, user type) [web]", "description": "The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.firstInputDelay.load.browser", "displayName": "First Input Delay - load action (by key user action, browser) [web]", "description": "The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.largestContentfulPaint.load.userType", "displayName": "Largest Contentful Paint - load action (by key user action, user type) [web]", "description": "The time taken to render the largest element in the viewport. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.largestContentfulPaint.load.userType.geo", "displayName": "Largest Contentful Paint - load action (by key user action, geolocation, user type) [web]", "description": "The time taken to render the largest element in the viewport. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.largestContentfulPaint.load.browser", "displayName": "Largest Contentful Paint - load action (by key user action, browser) [web]", "description": "The time taken to render the largest element in the viewport. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.loadEventEnd.load.browser", "displayName": "Load event end - load action (by key user action, browser) [web]", "description": "The time taken to complete the load event of a page. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.loadEventStart.load.browser", "displayName": "Load event start - load action (by key user action, browser) [web]", "description": "The time taken to begin the load event of a page. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.networkContribution.load", "displayName": "Network contribution - load action (by key user action, user type) [web]", "description": "The time taken to request and receive resources (including DNS lookup, redirect, and TCP connect time). Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.networkContribution.xhr", "displayName": "Network contribution - XHR action (by key user action, user type) [web]", "description": "The time taken to request and receive resources (including DNS lookup, redirect, and TCP connect time). Calculated for XHR actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.responseEnd.load.browser", "displayName": "Response end - load action (by key user action, browser) [web]", "description": "(AKA HTML downloaded) The time taken until the user agent receives the last byte of the response or the transport connection is closed, whichever comes first. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.responseEnd.xhr.browser", "displayName": "Response end - XHR action (by key user action, browser) [web]", "description": "The time taken until the user agent receives the last byte of the response or the transport connection is closed, whichever comes first. Calculated for XHR actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.serverContribution.load", "displayName": "Server contribution - load action (by key user action, user type) [web]", "description": "The time spent on server-side processing for a page. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.serverContribution.xhr", "displayName": "Server contribution - XHR action (by key user action, user type) [web]", "description": "The time spent on server-side processing for a page. Calculated for XHR actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.speedIndex.load.browser", "displayName": "Speed index - load action (by key user action, browser) [web]", "description": "The score measuring how quickly the visible parts of a page are rendered. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.visuallyComplete.load.browser", "displayName": "Visually complete - load action (by key user action, browser) [web]", "description": "The time taken to fully render content in the viewport. Calculated for load actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.visuallyComplete.xhr.browser", "displayName": "Visually complete - XHR action (by key user action, browser) [web]", "description": "The time taken to fully render content in the viewport. Calculated for XHR actions that are marked as key user actions.", "unit": "MilliSecond", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.action.countOfErrors", "displayName": "Error count (by key user action, user type, error type, error origin) [web]", "description": "The number of detected errors that occurred during key user actions.", "unit": "Count", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.countOfUserActionsWithErrors", "displayName": "User action count with errors (by key user action, user type) [web]", "description": "The number of key user actions with detected errors.", "unit": "Count", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.jsErrorsDuringUa", "displayName": "JavaScript errors count during user actions (by key user action, user type) [web]", "description": "The number of detected JavaScript errors that occurred during key user actions.", "unit": "Count", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.jsErrorsWithoutUa", "displayName": "JavaScript error count without user actions (by key user action, user type) [web]", "description": "The number of detected standalone JavaScript errors (occurred between key user actions).", "unit": "Count", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.action.percentageOfUserActionsAffectedByErrors", "displayName": "User action rate - affected by errors (by key user action, user type) [web]", "description": "The percentage of key user actions with detected errors.", "unit": "Percent", "entityType": ["APPLICATION_METHOD"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application_method", "name": "Application method", "displayName": "User Action", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.custom.browser", "displayName": "Action count - custom action (by browser) [web]", "description": "The number of custom actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.load.browser", "displayName": "Action count - load action (by browser) [web]", "description": "The number of load actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.xhr.browser", "displayName": "Action count - XHR action (by browser) [web]", "description": "The number of XHR actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.category", "displayName": "Action count (by Apdex category) [web]", "description": "The number of user actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Apdex category", "name": "Apdex category", "displayName": "Apdex category", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.actionCount.summary", "displayName": "Action with key performance metric count (by action type, geolocation, user type) [web]", "description": "The number of user actions that have a key performance metric and mapped geolocation.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}, {"key": "Action type", "name": "Action type", "displayName": "Action type", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.actionDuration.custom.browser", "displayName": "Action duration - custom action (by browser) [web]", "description": "The duration of custom actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionDuration.load.browser", "displayName": "Action duration - load action (by browser) [web]", "description": "The duration of load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionDuration.xhr.browser", "displayName": "Action duration - XHR action (by browser) [web]", "description": "The duration of XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.actionsPerSession", "displayName": "Actions per session average (by users, user type) [web]", "description": "The average number of user actions per user session.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.activeSessions", "displayName": "Session count - estimated active sessions (by users, user type) [web]", "description": "The estimated number of active user sessions. An active session is one in which a user has been confirmed to still be active at a given time. For this high-cardinality metric, the HyperLogLog algorithm is used to approximate the session count.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.activeUsersEst", "displayName": "User count - estimated active users (by users, user type) [web]", "description": "The estimated number of unique active users. For this high-cardinality metric, the HyperLogLog algorithm is used to approximate the user count.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.affectedUas", "displayName": "User action rate - affected by JavaScript errors (by user type) [web]", "description": "The percentage of user actions with detected JavaScript errors.", "unit": "Percent", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.apdex.userType", "displayName": "Apdex (by user type) [web]", "description": "", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.apdex.userType.geoBig", "displayName": "Apdex (by geolocation, user type) [web]", "description": "The average Apdex rating for user actions that have a mapped geolocation.", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.bouncedSessionRatio", "displayName": "Bounce rate (by users, user type) [web]", "description": "The percentage of sessions in which users viewed only a single page and triggered only a single web request. Calculated by dividing single-page sessions by all sessions.", "unit": "Percent", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.conversionRate", "displayName": "Conversion rate - sessions (by users, user type) [web]", "description": "The percentage of sessions in which at least one conversion goal was reached. Calculated by dividing converted sessions by all sessions.", "unit": "Percent", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.converted", "displayName": "Session count - converted sessions (by users, user type) [web]", "description": "The number of sessions in which at least one conversion goal was reached.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.cumulativeLayoutShift.load.userType", "displayName": "Cumulative Layout Shift - load action (by user type) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.cumulativeLayoutShift.load.userType.geo", "displayName": "Cumulative Layout Shift - load action (by geolocation, user type) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.cumulativeLayoutShift.load.browser", "displayName": "Cumulative Layout Shift - load action (by browser) [web]", "description": "The score measuring the unexpected shifting of visible webpage elements. Calculated for load actions.", "unit": "Unspecified", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.domInteractive.load.browser", "displayName": "DOM interactive - load action (by browser) [web]", "description": "The time taken until a page's status is set to \"interactive\" and it's ready to receive user input. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.endedSessions", "displayName": "Session count - estimated ended sessions (by users, user type) [web]", "description": "The number of completed user sessions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.event.count.rageClick", "displayName": "Rage click count [web]", "description": "The number of detected rage clicks.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.firstByte.load.browser", "displayName": "Time to first byte - load action (by browser) [web]", "description": "The time taken until the first byte of the response is received from the server, relevant application caches, or a local resource. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.firstByte.xhr.browser", "displayName": "Time to first byte - XHR action (by browser) [web]", "description": "The time taken until the first byte of the response is received from the server, relevant application caches, or a local resource. Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.firstInputDelay.load.userType", "displayName": "First Input Delay - load action (by user type) [web]", "description": "The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.firstInputDelay.load.userType.geo", "displayName": "First Input Delay - load action (by geolocation, user type) [web]", "description": "The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.firstInputDelay.load.browser", "displayName": "First Input Delay - load action (by browser) [web]", "description": "The time from the first interaction with a page to when the user agent can respond to that interaction. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.largestContentfulPaint.load.userType", "displayName": "Largest Contentful Paint - load action (by user type) [web]", "description": "The time taken to render the largest element in the viewport. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.largestContentfulPaint.load.userType.geo", "displayName": "Largest Contentful Paint - load action (by geolocation, user type) [web]", "description": "The time taken to render the largest element in the viewport. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": "Geolocation", "displayName": "Geolocation", "index": 1, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.largestContentfulPaint.load.browser", "displayName": "Largest Contentful Paint - load action (by browser) [web]", "description": "The time taken to render the largest element in the viewport. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.loadEventEnd.load.browser", "displayName": "Load event end - load action (by browser) [web]", "description": "The time taken to complete the load event of a page. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.loadEventStart.load.browser", "displayName": "Load event start - load action (by browser) [web]", "description": "The time taken to begin the load event of a page. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.networkContribution.load", "displayName": "Network contribution - load action (by user type) [web]", "description": "The time taken to request and receive resources (including DNS lookup, redirect, and TCP connect time). Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.networkContribution.xhr", "displayName": "Network contribution - XHR action (by user type) [web]", "description": "The time taken to request and receive resources (including DNS lookup, redirect, and TCP connect time). Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.responseEnd.load.browser", "displayName": "Response end - load action (by browser) [web]", "description": "(AKA HTML downloaded) The time taken until the user agent receives the last byte of the response or the transport connection is closed, whichever comes first. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.responseEnd.xhr.browser", "displayName": "Response end - XHR action (by browser) [web]", "description": "The time taken until the user agent receives the last byte of the response or the transport connection is closed, whichever comes first. Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.serverContribution.load", "displayName": "Server contribution - load action (by user type) [web]", "description": "The time spent on server-side processing for a page. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.serverContribution.xhr", "displayName": "Server contribution - XHR action (by user type) [web]", "description": "The time spent on server-side processing for a page. Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.sessionDuration", "displayName": "Session duration (by users, user type) [web]", "description": "The average duration of user sessions.", "unit": "MicroSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.speedIndex.load.browser", "displayName": "Speed index - load action (by browser) [web]", "description": "The score measuring how quickly the visible parts of a page are rendered. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.startedSessions", "displayName": "Session count - estimated started sessions (by users, user type) [web]", "description": "The number of started user sessions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Users", "name": "Users", "displayName": "Users", "index": 1, "type": "STRING"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.visuallyComplete.load.browser", "displayName": "Visually complete - load action (by browser) [web]", "description": "The time taken to fully render content in the viewport. Calculated for load actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.visuallyComplete.xhr.browser", "displayName": "Visually complete - XHR action (by browser) [web]", "description": "The time taken to fully render content in the viewport. Calculated for XHR actions.", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "avg", "count", "max", "median", "min", "percentile", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": "Browser", "displayName": "Browser", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.countOfErrors", "displayName": "Error count (by user type, error type, error origin) [web]", "description": "The number of detected errors.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.countOfErrorsDuringUserActions", "displayName": "Error count during user actions (by user type, error type, error origin) [web]", "description": "The number of detected errors that occurred during user actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.countOfStandaloneErrors", "displayName": "Standalone error count (by user type, error type, error origin) [web]", "description": "The number of detected standalone errors (occurred between user actions).", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.countOfUserActionsWithErrors", "displayName": "User action count - with errors (by user type) [web]", "description": "The number of key user actions with detected errors.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.errorCountForDavis", "displayName": "Error count for <PERSON> (by user type, error type, error origin, error context)) [web]", "description": "The number of errors that were included in Davis AI problem detection and analysis.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}, {"key": "Error type", "name": "Error type", "displayName": "Error type", "index": 2, "type": "STRING"}, {"key": "Error origin", "name": "Error origin", "displayName": "Error origin", "index": 3, "type": "STRING"}, {"key": "Error context", "name": "Error context", "displayName": "Error context", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.interactionToNextPaint", "displayName": "Interaction to next paint", "description": "", "unit": "MilliSecond", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "count", "max", "median", "min", "percentile"], "defaultAggregation": {"type": "percentile", "parameter": 50.0}, "dimensionDefinitions": [{"key": "User type", "name": null, "displayName": "User type", "index": null, "type": "STRING"}, {"key": "dt.entity.application", "name": null, "displayName": "Web application", "index": null, "type": "ENTITY"}, {"key": "dt.entity.browser", "name": null, "displayName": "Browser", "index": null, "type": "ENTITY"}, {"key": "dt.entity.geolocation", "name": null, "displayName": "Geolocation", "index": null, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:apps.web.jsErrorsDuringUa", "displayName": "JavaScript error count - during user actions (by user type) [web]", "description": "The number of detected JavaScript errors that occurred during user actions.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.jsErrorsWithoutUa", "displayName": "JavaScript error count - without user actions (by user type) [web]", "description": "The number of detected standalone JavaScript errors (occurred between user actions).", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:apps.web.percentageOfUserActionsAffectedByErrors", "displayName": "User action rate - affected by errors (by user type) [web]", "description": "The percentage of user actions with detected errors.", "unit": "Percent", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "User type", "name": "User type", "displayName": "User type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.apps.custom.sessionsWithoutReplayByApplication", "displayName": "Session count - billed and unbilled [custom]", "description": "The number of billed and unbilled user sessions.\n To get only the number of billed sessions, set the \"Type\" filter to \"Billed\".", "unit": "Count", "entityType": ["CUSTOM_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.custom_application", "name": "Custom application", "displayName": "Custom Application", "index": 0, "type": "ENTITY"}, {"key": "Type", "name": "Type", "displayName": "Type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.apps.custom.userActionPropertiesByDeviceApplication", "displayName": "Total user action and session properties", "description": "The number of billed user action and user session properties.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.custom_application", "name": "Custom application", "displayName": "Custom Application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.apps.mobile.sessionsWithReplayByApplication", "displayName": "Session count - billed and unbilled - with Session Replay [mobile]", "description": "The number of billed and unbilled user sessions that include Session Replay data. To get only the number of billed sessions, set the \"Type\" filter to \"Billed\".", "unit": "Count", "entityType": ["MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.mobile_application", "name": "Mobile application", "displayName": "Mobile App", "index": 0, "type": "ENTITY"}, {"key": "Type", "name": "Type", "displayName": "Type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.apps.mobile.sessionsWithoutReplayByApplication", "displayName": "Session count - billed and unbilled [mobile]", "description": "The total number of billed and unbilled user sessions (with and without Session Replay data). To get only the number of billed sessions, set the \"Type\" filter to \"Billed\".", "unit": "Count", "entityType": ["MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.mobile_application", "name": "Mobile application", "displayName": "Mobile App", "index": 0, "type": "ENTITY"}, {"key": "Type", "name": "Type", "displayName": "Type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.apps.mobile.userActionPropertiesByMobileApplication", "displayName": "Total user action and session properties", "description": "The number of billed user action and user session properties.", "unit": "Count", "entityType": ["MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.mobile_application", "name": "Mobile application", "displayName": "Mobile App", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.apps.web.sessionsWithReplayByApplication", "displayName": "Session count - billed and unbilled - with Session Replay [web]", "description": "The number of billed and unbilled user sessions that include Session Replay data. To get only the number of billed sessions, set the \"Type\" filter to \"Billed\".", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Type", "name": "Type", "displayName": "Type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.apps.web.sessionsWithoutReplayByApplication", "displayName": "Session count - billed and unbilled - without Session Replay [web]", "description": "The number of billed and unbilled user sessions that do not include Session Replay data. To get only the number of billed sessions, set the \"Type\" filter to \"Billed\".", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}, {"key": "Type", "name": "Type", "displayName": "Type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.apps.web.userActionPropertiesByApplication", "displayName": "Total user action and session properties", "description": "The number of billed user action and user session properties.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.custom_events_classic.usage", "displayName": "(DPS) Total Custom Events Classic billing usage", "description": "The number of custom events ingested aggregated over all monitored entities. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_events_classic.usage_by_entity", "displayName": "(DPS) Custom Events Classic billing usage by monitored entity", "description": "The number of custom events ingested split by monitored entity. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. For details on the events billed, refer to the usage_by_event_info metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.custom_events_classic.usage_by_event_info", "displayName": "(DPS) Custom Events Classic billing usage by event info", "description": "The number of custom events ingested split by event info. Custom events include events sent to Dynatrace via the Events API or events created by a log event extraction rule. The info contains the context of the event plus the configuration ID. For details on the related monitored entities, refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "event_info", "name": "event_info", "displayName": "event_info", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.raw.usage_by_metric_key", "displayName": "(DPS) Recorded metric data points per metric key", "description": "The number of reported metric data points split by metric key. This metric does not account for included metric data points available to your environment.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "metric_key", "name": "metric_key", "displayName": "metric_key", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage", "displayName": "(DPS) Total billed metric data points", "description": "The total number of metric data points after deducting the included metric data points. This is the rate-card value used for billing. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.foundation_and_discovery", "displayName": "(DPS) Total metric data points billable for Foundation & Discovery hosts", "description": "The number of metric data points billable for Foundation & Discovery hosts.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.fullstack_hosts", "displayName": "(DPS) Total metric data points billed for Full-Stack hosts", "description": "The number of metric data points billed for Full-Stack hosts. To view the unadjusted usage per host, use builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host . This trailing metric is reported at 15-minute intervals with up to a 15-minute delay.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.infrastructure_hosts", "displayName": "(DPS) Total metric data points billed for Infrastructure-monitored hosts", "description": "The number of metric data points billed for Infrastructure-monitored hosts. To view the unadjusted usage per host, use builtin:billing.infrastructure_monitoring.metric_data_points.ingested_by_host . This trailing metric is reported at 15-minute intervals with up to a 15-minute delay.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.other", "displayName": "(DPS) Total metric data points billed by other entities", "description": "The number of metric data points billed that cannot be assigned to a host. The values reported in this metric are not eligible for included metric deduction and will be billed as is. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. o view the monitored entities that consume this usage, use the other_by_entity metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_metrics_classic.usage.other_by_entity", "displayName": "(DPS) Billed metric data points reported and split by other entities", "description": "The number of billed metric data points split by entities that cannot be assigned to a host. The values reported in this metric are not eligible for included metric deduction and will be billed as is. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.custom_traces_classic.usage", "displayName": "(DPS) Total Custom Traces Classic billing usage", "description": "The number of spans ingested aggregated over all monitored entities. A span is a single operation within a distributed trace, ingested into Dynatrace. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.custom_traces_classic.usage_by_entity", "displayName": "(DPS) Custom Traces Classic billing usage by monitored entity", "description": "The number of spans ingested split by monitored entity. A span is a single operation within a distributed trace, ingested into Dynatrace. For details on span types, refer to the usage_by_span_type metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.custom_traces_classic.usage_by_span_type", "displayName": "(DPS) Custom Traces Classic billing usage by span type", "description": "The number of spans ingested split by span type. A span is a single operation within a distributed trace, ingested into Dynatrace. Span kinds can be CLIENT, SERVER, PRODUCER, CONSUMER or INTERNAL For details on the related monitored entities, refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "span_type", "name": "span_type", "displayName": "span_type", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.ddu.events.byDescription", "displayName": "DDU events consumption by event info", "description": "License consumption of Davis data units by events pool split by event info", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "Description", "name": "Description", "displayName": "Description", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.ddu.events.byEntity", "displayName": "DDU events consumption by monitored entity", "description": "License consumption of Davis data units by events pool split by monitored entity", "unit": "Unspecified", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.ddu.events.total", "displayName": "Total DDU events consumption", "description": "Sum of license consumption of Davis data units aggregated over all monitored entities for the events pool", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.ddu.log.byDescription", "displayName": "DDU log consumption by log path", "description": "License consumption of Davis data units by log pool split by log path", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "Description", "name": "Description", "displayName": "Description", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.ddu.log.byEntity", "displayName": "DDU log consumption by monitored entity", "description": "License consumption of Davis data units by log pool split by monitored entity", "unit": "Unspecified", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.ddu.log.total", "displayName": "Total DDU log consumption", "description": "Sum of license consumption of Davis data units aggregated over all logs for the log pool", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.ddu.metrics.byEntity", "displayName": "DDU metrics consumption by monitored entity", "description": "License consumption of Davis data units by metrics pool split by monitored entity", "unit": "Unspecified", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.ddu.metrics.byEntityRaw", "displayName": "DDU metrics consumption by monitored entity w/o host-unit included DDUs", "description": "License consumption of Davis data units by metrics pool split by monitored entity (aggregates host-unit included metrics, so value might be higher than actual consumption)", "unit": "Unspecified", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.ddu.metrics.byMetric", "displayName": "Reported metrics DDUs by metric key", "description": "Reported Davis data units usage by metrics pool split by metric key", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "Metric Key", "name": "Metric Key", "displayName": "Metric Key", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.ddu.metrics.total", "displayName": "Total DDU metrics consumption", "description": "Sum of license consumption of Davis data units aggregated over all metrics for the metrics pool", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.ddu.serverless.byDescription", "displayName": "DDU serverless consumption by function", "description": "License consumption of Davis data units by serverless pool split by Amazon Resource Names (ARNs)", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "Description", "name": "Description", "displayName": "Description", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.ddu.serverless.byEntity", "displayName": "DDU serverless consumption by service", "description": "License consumption of Davis data units by serverless pool split by service", "unit": "Unspecified", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.ddu.serverless.total", "displayName": "Total DDU serverless consumption", "description": "Sum of license consumption of Davis data units aggregated over all services for the serverless pool", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.ddu.traces.byDescription", "displayName": "DDU traces consumption by span type", "description": "License consumption of Davis data units by traces pool split by SpanKind, as defined in OpenTelemetry specification", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "Description", "name": "Description", "displayName": "Description", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.ddu.traces.byEntity", "displayName": "DDU traces consumption by monitored entity", "description": "License consumption of Davis data units by traces pool split by monitored entity", "unit": "Unspecified", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.ddu.traces.total", "displayName": "Total DDU traces consumption", "description": "Sum of license consumption of Davis data units aggregated over all monitored entities for the traces pool", "unit": "Unspecified", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.ddu.includedMetricDduPerHost", "displayName": "DDU included per host", "description": "Included Davis data units per host", "unit": "Unspecified", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.ddu.includedMetricPerHost", "displayName": "DDU included metric data points per host", "description": "Included metric data points per host", "unit": "Unspecified", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.events.business_events.ingest.usage", "displayName": "[Deprecated]  (DPS) Business events usage - Ingest & Process", "description": "Business events Ingest & Process usage, tracked as bytes ingested within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.events.business_events.query.usage", "displayName": "[Deprecated] (DPS) Business events usage - Query", "description": "Business events Query usage, tracked as bytes read within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.events.business_events.retain.usage", "displayName": "[Deprecated] (DPS) Business events usage - Retain", "description": "Business events Retain usage, tracked as total storage used within the hour, in bytes. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. [Deprecated] This metric is replaced by billing usage events.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.foundation_and_discovery.metric_data_points.ingested", "displayName": "(DPS) Ingested metric data points for Foundation & Discovery", "description": "The number of metric data points aggregated over all Foundation & Discovery hosts.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.foundation_and_discovery.metric_data_points.ingested_by_host", "displayName": "(DPS) Ingested metric data points for Foundation & Discovery per host", "description": "The number of metric data points split by Foundation & Discovery hosts.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.foundation_and_discovery.usage", "displayName": "(DPS) Foundation & Discovery billing usage", "description": "The total number of host-hours being monitored by Foundation & Discovery, counted in 15 min intervals.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.foundation_and_discovery.usage_per_host", "displayName": "(DPS) Foundation & Discovery billing usage per host", "description": "The host-hours being monitored by Foundation & Discovery, counted in 15 min intervals.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.metric_data_points.included", "displayName": "(DPS) Available included metric data points for Full-Stack hosts", "description": "The total number of included metric data points that can be deducted from the metric data points reported by Full-Stack hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of applied included metric data points, use builtin:billing.full_stack_monitoring.metric_data_points.included_used . If the difference between this metric and the applied metrics is greater than 0, then more metrics can be ingested using Full-Stack Monitoring without incurring additional costs.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.metric_data_points.included_used", "displayName": "(DPS) Used included metric data points for Full-Stack hosts", "description": "The number of consumed included metric data points per host monitored with Full-Stack Monitoring. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of potentially available included metrics, use builtin:billing.full_stack_monitoring.metric_data_points.included_used . If the difference between this metric and the available metrics is greater than zero, then that means that more metrics could be ingested on Full-Stack hosts without incurring additional costs.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.metric_data_points.ingested", "displayName": "(DPS) Total metric data points reported by Full-Stack hosts", "description": "The number of metric data points aggregated over all Full-Stack hosts. The values reported in this metric are eligible for included-metric-data-point deduction. Use this total metric to query longer timeframes without losing precision or performance. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view usage on a per-host basis, use builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host .", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host", "displayName": "(DPS) Metric data points reported and split by Full-Stack hosts", "description": "The number of metric data points split by Full-Stack hosts. The values reported in this metric are eligible for included-metric-data-point deduction. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. The pool of available included metrics for a \"15-minute interval\" is visible via builtin:billing.full_stack_monitoring.metric_data_points.included . To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.usage", "displayName": "(DPS) Full-Stack Monitoring billing usage", "description": "The total GiB memory of hosts being monitored in full-stack mode, counted in 15 min intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the hosts causing the usage, refer to the usage_per_host metric. For details on the containers causing the usage, refer to the usage_per_container metric.", "unit": "GibiByte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.usage_per_container", "displayName": "(DPS) Full-stack usage by container type", "description": "The total GiB memory of containers being monitored in full-stack mode, counted in 15 min intervals.", "unit": "GibiByte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "application_only_type", "name": null, "displayName": "application_only_type", "index": null, "type": "STRING"}, {"key": "dt.entity.cloud_application_namespace", "name": null, "displayName": "Kubernetes namespace", "index": null, "type": "ENTITY"}, {"key": "dt.entity.kubernetes_cluster", "name": null, "displayName": "Kubernetes cluster", "index": null, "type": "ENTITY"}, {"key": "k8s.cluster.uid", "name": null, "displayName": "k8s.cluster.uid", "index": null, "type": "STRING"}, {"key": "k8s.namespace.name", "name": null, "displayName": "k8s.namespace.name", "index": null, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.full_stack_monitoring.usage_per_host", "displayName": "(DPS) Full-Stack Monitoring billing usage per host", "description": "The GiB memory per host being monitored in full-stack mode, counted in 15 min intervals. For example, a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "GibiByte", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.metric_data_points.included", "displayName": "(DPS) Available included metric data points for Infrastructure-monitored hosts", "description": "The total number of included metric data points that can be deducted from the metric data points reported by Infrastructure-monitored hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of applied included metric data points, use builtin:billing.infrastructure_monitoring.metric_data_points.included_used . If the difference between this metric and the applied metrics is greater than zero, then that means that more metrics could be ingested on Infrastructure-monitored hosts without incurring additional costs.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.metric_data_points.included_used", "displayName": "(DPS) Used included metric data points for Infrastructure-monitored hosts", "description": "The number of consumed included metric data points for Infrastructure-monitored hosts. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view the number of potentially available included metrics, use builtin:billing.infrastructure_monitoring.metric_data_points.included_used . If the difference between this metric and the available metrics is greater than zero, then that means that more metrics could be ingested on Infrastructure-monitored hosts without incurring additional costs.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.metric_data_points.ingested", "displayName": "(DPS) Total metric data points reported by Infrastructure-monitored hosts", "description": "The number of metric data points aggregated over all Infrastructure-monitored hosts. The values reported in this metric are eligible for included-metric-data-point deduction. Use this total metric to query longer timeframes without losing precision or performance. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. To view usage on a per-host basis, use the builtin:billing.full_stack_monitoring.metric_data_points.ingested_by_host .", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.metric_data_points.ingested_by_host", "displayName": "(DPS) Metric data points reported and split by Infrastructure-monitored hosts", "description": "The number of metric data points split by Infrastructure-monitored hosts. The values reported in this metric are eligible for included-metric-data-point deduction. This trailing metric is reported at 15-minute intervals with up to a 15-minute delay. The pool of available included metrics for a \"15-minute interval\" is visible via builtin:billing.infrastructure_monitoring.metric_data_points.included . To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.usage", "displayName": "(DPS) Infrastructure Monitoring billing usage", "description": "The total number of host-hours being monitored in infrastructure-only mode, counted in 15 min intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the hosts causing the usage, refer to the usage_per_host metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.infrastructure_monitoring.usage_per_host", "displayName": "(DPS) Infrastructure Monitoring billing usage per host", "description": "The host-hours being monitored in infrastructure-only mode, counted in 15 min intervals. A host monitored for the whole hour has 4 data points with a value of 0.25, regardless of the memory size. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.kubernetes_monitoring.usage", "displayName": "(DPS) Kubernetes Platform Monitoring billing usage", "description": "The total number of monitored Kubernetes pods per hour, split by cluster and namespace and counted in 15 min intervals. A pod monitored for the whole hour has 4 data points with a value of 0.25.", "unit": "Count", "entityType": ["KUBERNETES_CLUSTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.kubernetes_cluster", "name": "Kubernetes cluster", "displayName": "Kubernetes cluster", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.cloud_application_namespace", "name": "Cloud application namespace", "displayName": "Kubernetes namespace", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.log.ingest.usage", "displayName": "(DPS) Log Management and Analytics usage - Ingest & Process", "description": "Log Management and Analytics Ingest & Process usage, tracked as bytes ingested within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.log.query.usage", "displayName": "(DPS) Log Management and Analytics usage - Query", "description": "Log Management and Analytics Query usage, tracked as bytes read within the hour. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.log.retain.usage", "displayName": "(DPS) Log Management and Analytics usage - Retain", "description": "Log Management and Analytics Retain usage, tracked as total storage used within the hour, in bytes. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay.", "unit": "Byte", "entityType": [], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.log_monitoring_classic.usage", "displayName": "(DPS) Total Log Monitoring Classic billing usage", "description": "The number of log records ingested aggregated over all monitored entities. A log record is recognized by either a timestamp or a JSON object. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.log_monitoring_classic.usage_by_entity", "displayName": "(DPS) Log Monitoring Classic billing usage by monitored entity", "description": "The number of log records ingested split by monitored entity. A log record is recognized by either a timestamp or a JSON object. For details on the log path, refer to the usage_by_log_path metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.log_monitoring_classic.usage_by_log_path", "displayName": "(DPS) Log Monitoring Classic billing usage by log path", "description": "The number of log records ingested split by log path. A log record is recognized by either a timestamp or a JSON object. For details on the related monitored entities, refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "log_path", "name": "log_path", "displayName": "log_path", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.mainframe_monitoring.usage", "displayName": "(DPS) Mainframe Monitoring billing usage", "description": "The total number of MSU-hours being monitored, counted in 15 min intervals.", "unit": "MSU", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.property.usage", "displayName": "(DPS) Total Real-User Monitoring Property (mobile) billing usage", "description": "(Mobile) User action and session properties count. For details on how usage is calculated, refer to the documentation or builtin:billing.real_user_monitoring.web.property.usage_by_application . Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.property.usage_by_application", "displayName": "(DPS) Real-User Monitoring Property (mobile) billing usage by application", "description": "(Mobile) User action and session properties count by application. The billed value is calculated based on the number of sessions reported in builtin:billing.real_user_monitoring.mobile.session.usage_by_app + builtin:billing.real_user_monitoring.mobile.session_with_replay.usage_by_app . plus the number of configured properties that exceed the included number of properties (free of charge) offered for a given application. Data points are only written for billed sessions. If the value is 0, you have available metric data points. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.session.usage", "displayName": "(DPS) Total Real-User Monitoring (mobile) billing usage", "description": "(Mobile) Session count without Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute, then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage, refer to the usage_by_app metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.session.usage_by_app", "displayName": "(DPS) Real-User Monitoring (mobile) billing usage by application", "description": "(Mobile) Session count without Session Replay split by application The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute, then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.session_with_replay.usage", "displayName": "(DPS) Total Real-User Monitoring (mobile) with Session Replay billing usage", "description": "(Mobile) Session count with Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute, then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage, refer to the usage_by_app metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.mobile.session_with_replay.usage_by_app", "displayName": "(DPS) Real-User Monitoring (mobile) with Session Replay billing usage by application", "description": "(Mobile) Session count with Session Replay split by application. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute, then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["CUSTOM_APPLICATION", "MOBILE_APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.device_application", "name": "Application", "displayName": "Mobile or custom application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.property.usage", "displayName": "(DPS) Total Real-User Monitoring Property (web) billing usage", "description": "(Web) User action and session properties count. For details on how usage is calculated, refer to the documentation or builtin:billing.real_user_monitoring.web.property.usage_by_application . Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.property.usage_by_application", "displayName": "(DPS) Real-User Monitoring Property (web) billing usage by application", "description": "(Web) User action and session properties count by application. The billed value is calculated based on the number of sessions reported in builtin:billing.real_user_monitoring.web.session.usage_by_app + builtin:billing.real_user_monitoring.web.session_with_replay.usage_by_app . plus the number of configured properties that exceed the included number of properties (free of charge) offered for a given application. Data points are only written for billed sessions. If the value is 0, you have available metric data points. This trailing metric is reported hourly for the previous hour. Metric values are reported with up to a one-hour delay. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.session.usage", "displayName": "(DPS) Total Real-User Monitoring (web) billing usage", "description": "(Web) Session count without Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute, then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage, refer to the usage_by_app metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.session.usage_by_app", "displayName": "(DPS) Real-User Monitoring (web) billing usage by application", "description": "(Web) Session count without Session Replay split by application. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute, then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.session_with_replay.usage", "displayName": "(DPS) Total Real-User Monitoring (web) with Session Replay billing usage", "description": "(Web) Session count with Session Replay. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions end during the same minute, then the values are added together. Use this total metric to query longer timeframes without losing precision or performance. To view the application that consume this usage, refer to the usage_by_app metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.real_user_monitoring.web.session_with_replay.usage_by_app", "displayName": "(DPS) Real-User Monitoring (web) with Session Replay billing usage by application", "description": "(Web) Session count with Session Replay split by application. The value billed for each session is the session duration measured in hours. So a 3-hour session results in a single data-point value of `3`. If two sessions of the same application end during the same minute, then the values are added together. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APPLICATION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.application", "name": "Application", "displayName": "Web application", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.runtime_application_protection.usage", "displayName": "(DPS) Runtime Application Protection billing usage", "description": "Total GiB-memory of hosts protected with Runtime Application Protection (Application Security), counted at 15-minute intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the monitored hosts, refer to the usage_per_host metric.", "unit": "GibiByte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.runtime_application_protection.usage_per_host", "displayName": "(DPS) Runtime Application Protection billing usage per host", "description": "GiB-memory per host protected with Runtime Application Protection (Application Security), counted at 15-minute intervals. For example, a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "GibiByte", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.runtime_vulnerability_analytics.usage", "displayName": "(DPS) Runtime Vulnerability Analytics billing usage", "description": "Total GiB-memory of hosts protected with Runtime Vulnerability Analytics (Application Security), counted at 15-minute intervals. Use this total metric to query longer timeframes without losing precision or performance. For details on the monitored hosts, refer to the usage_per_host metric.", "unit": "GibiByte", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.runtime_vulnerability_analytics.usage_per_host", "displayName": "(DPS) Runtime Vulnerability Analytics billing usage per host", "description": "GiB-memory per hosts protected with Runtime Vulnerability Analytics (Application Security), counted at 15-minute intervals. For example, a host with 8 GiB of RAM monitored for 1 hour has 4 data points with a value of `2`. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "GibiByte", "entityType": ["HOST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.host", "name": "Host", "displayName": "Host", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.serverless_functions_classic.usage", "displayName": "(DPS) Total Serverless Functions Classic billing usage", "description": "The number of invocations of the serverless function aggregated over all monitored entities. The term \"function invocations\" is equivalent to \"function requests\" or \"function executions\". Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.serverless_functions_classic.usage_by_entity", "displayName": "(DPS) Serverless Functions Classic billing usage by monitored entity", "description": "The number of invocations of the serverless function split by monitored entity. The term \"function invocations\" is equivalent to \"function requests\" or \"function executions\". For details on which functions are invoked, refer to the usage_by_function metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["APM_AGENT", "APM_CLUSTER", "APM_CLUSTER_NODE", "APM_SECURITY_GATEWAY", "APM_SERVER", "APM_TENANT", "APPLICATION", "APPLICATION_METHOD", "APPLICATION_METHOD_GROUP", "APPMON_SERVER", "APPMON_SYSTEM_PROFILE", "AUTO_SCALING_GROUP", "AWS_APPLICATION_LOAD_BALANCER", "AWS_AVAILABILITY_ZONE", "AWS_CREDENTIALS", "AWS_LAMBDA_FUNCTION", "AWS_NETWORK_LOAD_BALANCER", "AZURE_API_MANAGEMENT_SERVICE", "AZURE_APPLICATION_GATEWAY", "AZURE_APP_SERVICE_PLAN", "AZURE_COSMOS_DB", "AZURE_CREDENTIALS", "AZURE_EVENT_HUB", "AZURE_EVENT_HUB_NAMESPACE", "AZURE_FUNCTION_APP", "AZURE_IOT_HUB", "AZURE_LOAD_BALANCER", "AZURE_MGMT_GROUP", "AZURE_REDIS_CACHE", "AZURE_REGION", "AZURE_SERVICE_BUS_NAMESPACE", "AZURE_SERVICE_BUS_QUEUE", "AZURE_SERVICE_BUS_TOPIC", "AZURE_SQL_DATABASE", "AZURE_SQL_ELASTIC_POOL", "AZURE_SQL_SERVER", "AZURE_STORAGE_ACCOUNT", "AZURE_SUBSCRIPTION", "AZURE_TENANT", "AZURE_VM", "AZURE_VM_SCALE_SET", "AZURE_WEB_APP", "BOSH_DEPLOYMENT", "BROWSER", "CF_APPLICATION", "CF_APPLICATION_INSTANCE", "CF_FOUNDATION", "CF_ORG", "CF_SPACE", "CINDER_VOLUME", "CLOUD_APPLICATION", "CLOUD_APPLICATION_INSTANCE", "CLOUD_APPLICATION_NAMESPACE", "CLOUD_NETWORK_INGRESS", "CLOUD_NETWORK_POLICY", "CONTAINER_GROUP", "CONTAINER_GROUP_INSTANCE", "CREDENTIALS_VAULT", "CUSTOM_APPLICATION", "CUSTOM_DEVICE", "CUSTOM_DEVICE_GROUP", "DATASTORE", "DB_ENDPOINT", "DB_ENDPOINT_GROUP", "DCRUM_APPLICATION", "DCRUM_SERVICE", "DCRUM_SERVICE_INSTANCE", "DEVICE_APPLICATION_METHOD", "DEVICE_APPLICATION_METHOD_GROUP", "DISK", "DOCKER_CONTAINER_GROUP", "DOCKER_CONTAINER_GROUP_INSTANCE", "DYNAMO_DB_TABLE", "EBS_VOLUME", "EC2_INSTANCE", "ELASTIC_LOAD_BALANCER", "ENVIRONMENT", "EXTENSION_TASK_CONFIGURATION", "EXTERNAL_SYNTHETIC_TEST", "EXTERNAL_SYNTHETIC_TEST_STEP", "GCP_ZONE", "GEOLOCATION", "GEOLOC_SITE", "GOOGLE_COMPUTE_ENGINE", "GRAIL_BUSINESS_EVENTS_ANALYZE", "GRAIL_BUSINESS_EVENTS_INGEST", "GRAIL_BUSINESS_EVENTS_RETAIN", "GRAIL_LOG_ANALYZE", "GRAIL_LOG_INGEST", "GRAIL_LOG_RETAIN", "HOST", "HOST_GROUP", "HTTP_CHECK", "HTTP_CHECK_STEP", "HYPERVISOR", "HYPERVISOR_CLUSTER", "HYPERVISOR_DISK", "KUBERNETES_CLUSTER", "KUBERNETES_NODE", "KUBERNETES_SERVICE", "LOG_INSTANCE", "MEASUREMENT", "MEASUREMENT_GROUP", "MOBILE_APPLICATION", "MULTIPROTOCOL_MONITOR", "NETWORK_INTERFACE", "NEUTRON_SUBNET", "OPENSTACK_AVAILABILITY_ZONE", "OPENSTACK_COMPUTE_NODE", "OPENSTACK_CREDENTIALS", "OPENSTACK_PROJECT", "OPENSTACK_REGION", "OPENSTACK_VM", "OS", "PROCESS_GROUP", "PROCESS_GROUP_INSTANCE", "QUEUE", "QUEUE_INSTANCE", "RELATIONAL_DATABASE_SERVICE", "REMOTE_PLUGIN_MODULE", "REQUEST_ATTRIBUTE", "RUNTIME_COMPONENT", "S3BUCKET", "SERVICE", "SERVICE_INSTANCE", "SERVICE_METHOD", "SERVICE_METHOD_GROUP", "SOFTWARE_COMPONENT", "SWIFT_CONTAINER", "SYNTHETIC_LOCATION", "SYNTHETIC_TEST", "SYNTHETIC_TEST_STEP", "VCENTER", "VIRTUALMACHINE", "VMWARE_DATACENTER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.monitored_entity", "name": "Monitored entity", "displayName": "MONITORED_ENTITY", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.serverless_functions_classic.usage_by_function", "displayName": "(DPS) Serverless Functions Classic billing usage by function", "description": "The number of invocations of the serverless function split by function. The term \"function invocations\" is equivalent to \"function requests\" or \"function executions\". For details on the related monitored entities, refer to the usage_by_entity metric. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "function", "name": "function", "displayName": "function", "index": 0, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.synthetic.actions", "displayName": "Actions", "description": "The number of billed actions consumed by browser monitors.", "unit": "Count", "entityType": ["SYNTHETIC_TEST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.synthetic_test", "name": "Synthetic monitor", "displayName": "Synthetic monitor", "index": 0, "type": "ENTITY"}, {"key": "Location type", "name": "Location type", "displayName": "Location type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.synthetic.actions.usage", "displayName": "(DPS) Total Browser Monitor or Clickpath billing usage", "description": "The number of synthetic actions which triggers a web request that includes a page load, navigation event, or action that triggers an XHR or Fetch request. Scroll downs, keystrokes, or clicks that don't trigger web requests aren't counted as such actions. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.synthetic.actions.usage_by_browser_monitor", "displayName": "(DPS) Browser Monitor or Clickpath billing usage per synthetic browser monitor", "description": "The number of synthetic actions which triggers a web request that includes a page load, navigation event, or action that triggers an XHR or Fetch request. Scroll downs, keystrokes, or clicks that don't trigger web requests aren't counted as such actions. Actions are split by the Synthetic Browser Monitors that caused them. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["SYNTHETIC_TEST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.synthetic_test", "name": "Synthetic monitor", "displayName": "Synthetic monitor", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.synthetic.external", "displayName": "Third-party results", "description": "The number of billed results consumed by third-party monitors.", "unit": "Count", "entityType": ["EXTERNAL_SYNTHETIC_TEST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.external_synthetic_test", "name": "Synthetic monitor", "displayName": "Third-party synthetic test", "index": 0, "type": "ENTITY"}, {"key": "Location type", "name": "Location type", "displayName": "Location type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.synthetic.external.usage", "displayName": "(DPS) Total Third-Party Synthetic API Ingestion billing usage", "description": "The number of synthetic test results pushed into Dynatrace with Synthetic 3rd party API. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.synthetic.external.usage_by_third_party_monitor", "displayName": "(DPS) Third-Party Synthetic API Ingestion billing usage per external browser monitor", "description": "The number of synthetic test results pushed into Dynatrace with Synthetic 3rd party API. The ingestions are split by external Synthetic Browser Monitors for which the results where ingested. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["EXTERNAL_SYNTHETIC_TEST"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.external_synthetic_test", "name": "Synthetic monitor", "displayName": "Third-party synthetic test", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:billing.synthetic.requests", "displayName": "Requests", "description": "The number of billed requests consumed by HTTP monitors.", "unit": "Count", "entityType": ["HTTP_CHECK"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.http_check", "name": "Synthetic monitor", "displayName": "HTTP monitor", "index": 0, "type": "ENTITY"}, {"key": "Location type", "name": "Location type", "displayName": "Location type", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:billing.synthetic.requests.usage", "displayName": "(DPS) Total HTTP monitor billing usage", "description": "The number of HTTP requests performed during execution of synthetic HTTP monitor. Use this total metric to query longer timeframes without losing precision or performance.", "unit": "Count", "entityType": [], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [], "tags": []}, {"metricId": "builtin:billing.synthetic.requests.usage_by_http_monitor", "displayName": "(DPS) HTTP monitor billing usage per HTTP monitor", "description": "The number of HTTP requests performed, split by synthetic HTTP monitor. To improve performance and avoid exceeding query limits when working with longer timeframes, use the total metric.", "unit": "Count", "entityType": ["HTTP_CHECK"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.http_check", "name": "Synthetic monitor", "displayName": "HTTP monitor", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.connections.active", "displayName": "ALB number of active connections", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.connections.new", "displayName": "ALB number of new connections", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.alb.http4xx", "displayName": "ALB number of 4XX errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.alb.http5xx", "displayName": "ALB number of 5XX errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.target.http4xx", "displayName": "ALB number of 4XX target errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.target.http5xx", "displayName": "ALB number of 5XX target errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.rejCon", "displayName": "ALB number of rejected connections", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.targConn", "displayName": "ALB number of target connection errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.errors.tlsNeg", "displayName": "ALB number of client TLS negotiation errors", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.bytes", "displayName": "ALB number of processed bytes", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.lcus", "displayName": "ALB number of consumed LCUs", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.requests", "displayName": "ALB number of requests", "description": "", "unit": "Count", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.alb.respTime", "displayName": "ALB target response time", "description": "", "unit": "Second", "entityType": ["AWS_APPLICATION_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_application_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Application Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.asg.running", "displayName": "Number of running EC2 instances (ASG)", "description": "", "unit": "Count", "entityType": ["AUTO_SCALING_GROUP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.auto_scaling_group", "name": "AWS Auto Scaling group", "displayName": "Auto Scaling Group", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.asg.stopped", "displayName": "Number of stopped EC2 instances (ASG)", "description": "", "unit": "Count", "entityType": ["AUTO_SCALING_GROUP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.auto_scaling_group", "name": "AWS Auto Scaling group", "displayName": "Auto Scaling Group", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.asg.terminated", "displayName": "Number of terminated EC2 instances (ASG)", "description": "", "unit": "Count", "entityType": ["AUTO_SCALING_GROUP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.auto_scaling_group", "name": "AWS Auto Scaling group", "displayName": "Auto Scaling Group", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.az.running", "displayName": "Number of running EC2 instances (AZ)", "description": "", "unit": "Count", "entityType": ["AWS_CREDENTIALS"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_credentials", "name": "AWS account", "displayName": "AWS Credentials", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.aws_availability_zone", "name": "AWS Availability Zone", "displayName": "AWS Availability Zone", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.az.stopped", "displayName": "Number of stopped EC2 instances (AZ)", "description": "", "unit": "Count", "entityType": ["AWS_CREDENTIALS"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_credentials", "name": "AWS account", "displayName": "AWS Credentials", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.aws_availability_zone", "name": "AWS Availability Zone", "displayName": "AWS Availability Zone", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.az.terminated", "displayName": "Number of terminated EC2 instances (AZ)", "description": "", "unit": "Count", "entityType": ["AWS_CREDENTIALS"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_credentials", "name": "AWS account", "displayName": "AWS Credentials", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.aws_availability_zone", "name": "AWS Availability Zone", "displayName": "AWS Availability Zone", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.consumed.read", "displayName": "DynamoDB read capacity units", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.consumed.write", "displayName": "DynamoDB write capacity units", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.provisioned.read", "displayName": "DynamoDB provisioned read capacity units", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.provisioned.write", "displayName": "DynamoDB provisioned write capacity units", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.read", "displayName": "DynamoDB read capacity units %", "description": "", "unit": "Percent", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.capacityUnits.write", "displayName": "DynamoDB write capacity units %", "description": "", "unit": "Percent", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.errors.system", "displayName": "DynamoDB number of requests with HTTP 500 status code", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.errors.user", "displayName": "DynamoDB number of requests with HTTP 400 status code", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.requests.latency", "displayName": "DynamoDB number of successful request latency for operation", "description": "", "unit": "MilliSecond", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}, {"key": "Operation", "name": "Operation", "displayName": "Operation", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.requests.returnedItems", "displayName": "DynamoDB number of items returned by operation", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}, {"key": "Operation", "name": "Operation", "displayName": "Operation", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.requests.throttled", "displayName": "DynamoDB number of throttled requests for operation", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}, {"key": "Operation", "name": "Operation", "displayName": "Operation", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.throttledEvents.read", "displayName": "DynamoDB number of read throttled events", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.throttledEvents.write", "displayName": "DynamoDB number of write throttled events", "description": "", "unit": "Count", "entityType": ["DYNAMO_DB_TABLE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.dynamo_db_table", "name": "DynamoDB", "displayName": "DynamoDB Table", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.dynamo.tables", "displayName": "Number of tables for AvailabilityZone", "description": "", "unit": "Count", "entityType": ["AWS_CREDENTIALS"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_credentials", "name": "AWS account", "displayName": "AWS Credentials", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.aws_availability_zone", "name": "AWS Availability Zone", "displayName": "AWS Availability Zone", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.latency.read", "displayName": "EBS volume read latency", "description": "", "unit": "Second", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.latency.write", "displayName": "EBS volume write latency", "description": "", "unit": "Second", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.ops.consumed", "displayName": "EBS volume consumed OPS", "description": "", "unit": "PerSecond", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.ops.read", "displayName": "EBS volume read OPS", "description": "", "unit": "PerSecond", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.ops.write", "displayName": "EBS volume write OPS", "description": "", "unit": "PerSecond", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.throughput.percent", "displayName": "EBS volume throughput %", "description": "", "unit": "Percent", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.throughput.read", "displayName": "EBS volume read throughput", "description": "", "unit": "PerSecond", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.throughput.write", "displayName": "EBS volume write throughput", "description": "", "unit": "PerSecond", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.idleTime", "displayName": "EBS volume idle time %", "description": "", "unit": "Percent", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ebs.queue", "displayName": "EBS volume queue length", "description": "", "unit": "Count", "entityType": ["EBS_VOLUME"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ebs_volume", "name": "Amazon EBS", "displayName": "EBS Volume", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ec2.cpu.usage", "displayName": "EC2 CPU usage %", "description": "", "unit": "Percent", "entityType": ["EC2_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ec2_instance", "name": "AWS EC2", "displayName": "EC2 instance", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ec2.disk.readOps", "displayName": "EC2 instance storage read IOPS", "description": "", "unit": "PerSecond", "entityType": ["EC2_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ec2_instance", "name": "AWS EC2", "displayName": "EC2 instance", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ec2.disk.readRate", "displayName": "EC2 instance storage read rate", "description": "", "unit": "KiloBytePerSecond", "entityType": ["EC2_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ec2_instance", "name": "AWS EC2", "displayName": "EC2 instance", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ec2.disk.writeOps", "displayName": "EC2 instance storage write IOPS", "description": "", "unit": "PerSecond", "entityType": ["EC2_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ec2_instance", "name": "AWS EC2", "displayName": "EC2 instance", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ec2.disk.writeRate", "displayName": "EC2 instance storage write rate", "description": "", "unit": "KiloBytePerSecond", "entityType": ["EC2_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ec2_instance", "name": "AWS EC2", "displayName": "EC2 instance", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ec2.net.rx", "displayName": "EC2 network data received rate", "description": "", "unit": "BytePerSecond", "entityType": ["EC2_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ec2_instance", "name": "AWS EC2", "displayName": "EC2 instance", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.ec2.net.tx", "displayName": "EC2 network data transmitted rate", "description": "", "unit": "BytePerSecond", "entityType": ["EC2_INSTANCE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.ec2_instance", "name": "AWS EC2", "displayName": "EC2 instance", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.connection", "displayName": "CLB backend connection errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.http2xx", "displayName": "CLB number of backend 2XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.http3xx", "displayName": "CLB number of backend 3XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.http4xx", "displayName": "CLB number of backend 4XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.backend.http5xx", "displayName": "CLB number of backend 5XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.elb.http4xx", "displayName": "CLB number of 4XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.elb.http5xx", "displayName": "CLB number of 5XX errors", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.errors.frontend", "displayName": "CLB frontend errors percentage", "description": "", "unit": "Percent", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.hosts.healthy", "displayName": "CLB number of healthy hosts", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.hosts.unhealthy", "displayName": "CLB number of unhealthy hosts", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.latency", "displayName": "CLB latency", "description": "", "unit": "Second", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.elb.reqCompl", "displayName": "CLB number of completed requests", "description": "", "unit": "Count", "entityType": ["ELASTIC_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.elastic_load_balancer", "name": "AWS Classic Load Balancer", "displayName": "Classic Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.concExecutions", "displayName": "LambdaFunction concurrent executions count", "description": "", "unit": "Count", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.duration", "displayName": "LambdaFunction code execution time.", "description": "", "unit": "MilliSecond", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.errors", "displayName": "LambdaFunction number of failed invocations with HTTP 4XX status code", "description": "", "unit": "Count", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.errorsRate", "displayName": "LambdaFunction rate of failed invocations to all invocations %", "description": "", "unit": "Percent", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.invocations", "displayName": "LambdaFunction number of times a function is invoked", "description": "", "unit": "Count", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.provConcExecutions", "displayName": "LambdaFunction provisioned concurrent executions count", "description": "", "unit": "Count", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.provConcInvocations", "displayName": "LambdaFunction provisioned concurrency invocation count", "description": "", "unit": "Count", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.provConcSpilloverInvocations", "displayName": "LambdaFunction provisioned concurrency spillover invocation count", "description": "", "unit": "Count", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.lambda.throttlers", "displayName": "LambdaFunction throttled function invocation count", "description": "", "unit": "Count", "entityType": ["AWS_LAMBDA_FUNCTION"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_lambda_function", "name": "Lambda", "displayName": "AWS Lambda Function", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.flow.active", "displayName": "NLB number of active flows", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.flow.new", "displayName": "NLB number of new flows", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.tcp.reset.client", "displayName": "NLB number of client resets", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.tcp.reset.elb", "displayName": "NLB number of resets", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.tcp.reset.target", "displayName": "NLB number of target resets", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.bytes", "displayName": "NLB number of processed bytes", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.nlb.lcus", "displayName": "NLB number of consumed LCUs", "description": "", "unit": "Count", "entityType": ["AWS_NETWORK_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.aws_network_load_balancer", "name": "AWS Network Load Balancer", "displayName": "Network Load Balancer", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.cpu.usage", "displayName": "RDS CPU usage %", "description": "", "unit": "Percent", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.latency.read", "displayName": "RDS read latency", "description": "", "unit": "Second", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.latency.write", "displayName": "RDS write latency", "description": "", "unit": "Second", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.memory.freeable", "displayName": "RDS freeable memory", "description": "", "unit": "Byte", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.memory.swap", "displayName": "RDS swap usage", "description": "", "unit": "Byte", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.net.rx", "displayName": "RDS network received throughput", "description": "", "unit": "BytePerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.net.tx", "displayName": "RDS network transmitted throughput", "description": "", "unit": "BytePerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.ops.read", "displayName": "RDS read IOPS", "description": "", "unit": "PerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.ops.write", "displayName": "RDS write IOPS", "description": "", "unit": "PerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.throughput.read", "displayName": "RDS read throughput", "description": "", "unit": "BytePerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.throughput.write", "displayName": "RDS write throughput", "description": "", "unit": "BytePerSecond", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.connections", "displayName": "RDS connections", "description": "", "unit": "Count", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.free", "displayName": "RDS free storage space %", "description": "", "unit": "Percent", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.aws.rds.restarts", "displayName": "RDS restarts", "description": "", "unit": "Count", "entityType": ["RELATIONAL_DATABASE_SERVICE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.relational_database_service", "name": "Amazon RDS", "displayName": "Relational Database Service", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.apiMgmt.requests.failed", "displayName": "Failed requests", "description": "", "unit": "Count", "entityType": ["AZURE_API_MANAGEMENT_SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_api_management_service", "name": "Azure API Management Service", "displayName": "Azure API Management Service", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Host Name", "name": "Host Name", "displayName": "Host Name", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.apiMgmt.requests.other", "displayName": "Other requests", "description": "", "unit": "Count", "entityType": ["AZURE_API_MANAGEMENT_SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_api_management_service", "name": "Azure API Management Service", "displayName": "Azure API Management Service", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Host Name", "name": "Host Name", "displayName": "Host Name", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.apiMgmt.requests.successful", "displayName": "Successful requests", "description": "", "unit": "Count", "entityType": ["AZURE_API_MANAGEMENT_SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_api_management_service", "name": "Azure API Management Service", "displayName": "Azure API Management Service", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Host Name", "name": "Host Name", "displayName": "Host Name", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.apiMgmt.requests.total", "displayName": "Total requests", "description": "", "unit": "Count", "entityType": ["AZURE_API_MANAGEMENT_SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_api_management_service", "name": "Azure API Management Service", "displayName": "Azure API Management Service", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Host Name", "name": "Host Name", "displayName": "Host Name", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.apiMgmt.requests.unauth", "displayName": "Unauthorized requests", "description": "", "unit": "Count", "entityType": ["AZURE_API_MANAGEMENT_SERVICE"], "aggregationTypes": ["auto", "avg", "count", "max", "min", "sum"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_api_management_service", "name": "Azure API Management Service", "displayName": "Azure API Management Service", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Host Name", "name": "Host Name", "displayName": "Host Name", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.apiMgmt.capacity", "displayName": "Capacity", "description": "", "unit": "Percent", "entityType": ["AZURE_API_MANAGEMENT_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_api_management_service", "name": "Azure API Management Service", "displayName": "Azure API Management Service", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.apiMgmt.duration", "displayName": "Duration", "description": "", "unit": "MilliSecond", "entityType": ["AZURE_API_MANAGEMENT_SERVICE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_api_management_service", "name": "Azure API Management Service", "displayName": "Azure API Management Service", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Host Name", "name": "Host Name", "displayName": "Host Name", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appGateway.backend.settings.pool.host.healthy", "displayName": "Healthy host count", "description": "", "unit": "Count", "entityType": ["AZURE_APPLICATION_GATEWAY"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_application_gateway", "name": "Azure Application Gateway", "displayName": "Azure Application Gateway", "index": 0, "type": "ENTITY"}, {"key": "BackendSettingsPool", "name": "BackendSettingsPool", "displayName": "BackendSettingsPool", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appGateway.backend.settings.pool.host.unhealthy", "displayName": "Unhealthy host count", "description": "", "unit": "Count", "entityType": ["AZURE_APPLICATION_GATEWAY"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_application_gateway", "name": "Azure Application Gateway", "displayName": "Azure Application Gateway", "index": 0, "type": "ENTITY"}, {"key": "BackendSettingsPool", "name": "BackendSettingsPool", "displayName": "BackendSettingsPool", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appGateway.backend.settings.traffic.requests.failed", "displayName": "Requests failed", "description": "", "unit": "Count", "entityType": ["AZURE_APPLICATION_GATEWAY"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_application_gateway", "name": "Azure Application Gateway", "displayName": "Azure Application Gateway", "index": 0, "type": "ENTITY"}, {"key": "BackendSettingsPool", "name": "BackendSettingsPool", "displayName": "BackendSettingsPool", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appGateway.backend.settings.traffic.requests.total", "displayName": "Requests total", "description": "", "unit": "Count", "entityType": ["AZURE_APPLICATION_GATEWAY"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_application_gateway", "name": "Azure Application Gateway", "displayName": "Azure Application Gateway", "index": 0, "type": "ENTITY"}, {"key": "BackendSettingsPool", "name": "BackendSettingsPool", "displayName": "BackendSettingsPool", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appGateway.http.status.response", "displayName": "Response status", "description": "", "unit": "Count", "entityType": ["AZURE_APPLICATION_GATEWAY"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_application_gateway", "name": "Azure Application Gateway", "displayName": "Azure Application Gateway", "index": 0, "type": "ENTITY"}, {"key": "HttpStatusGroup", "name": "HttpStatusGroup", "displayName": "HttpStatusGroup", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appGateway.network.connections.count", "displayName": "Current connections count", "description": "", "unit": "Count", "entityType": ["AZURE_APPLICATION_GATEWAY"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_application_gateway", "name": "Azure Application Gateway", "displayName": "Azure Application Gateway", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.appGateway.network.throughput", "displayName": "Network throughput", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_APPLICATION_GATEWAY"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_application_gateway", "name": "Azure Application Gateway", "displayName": "Azure Application Gateway", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.applicationQueue.requests", "displayName": "Requests in application queue", "description": "", "unit": "Count", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.applicationQueue.requests", "displayName": "Requests in application queue", "description": "", "unit": "Count", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.execution.count", "displayName": "Function execution count", "description": "", "unit": "Count", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.execution.unitsCount", "displayName": "Function execution units count", "description": "", "unit": "Count", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.http.status.http5xx", "displayName": "HTTP 5xx", "description": "", "unit": "Count", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.io.operations.other", "displayName": "IO other operations/s", "description": "", "unit": "PerSecond", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.io.operations.read", "displayName": "IO read operations/s", "description": "", "unit": "PerSecond", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.io.operations.write", "displayName": "IO write operations/s", "description": "", "unit": "PerSecond", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.io.other", "displayName": "IO other bytes/s", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.io.read", "displayName": "IO read bytes/s", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.io.write", "displayName": "IO write bytes/s", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.traffic.bytesReceived", "displayName": "Received bytes", "description": "", "unit": "Byte", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.functions.traffic.bytesSent", "displayName": "Sent bytes", "description": "", "unit": "Byte", "entityType": ["AZURE_FUNCTION_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_function_app", "name": "Azure Function", "displayName": "Azure Function App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.http.status.http2xx", "displayName": "HTTP 2xx", "description": "", "unit": "Count", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.http.status.http403", "displayName": "HTTP 403", "description": "", "unit": "Count", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.http.status.http5xx", "displayName": "HTTP 5xx", "description": "", "unit": "Count", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.io.operations.other", "displayName": "IO other operations/s", "description": "", "unit": "PerSecond", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.io.operations.read", "displayName": "IO read operations/s", "description": "", "unit": "PerSecond", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.io.operations.write", "displayName": "IO write operations/s", "description": "", "unit": "PerSecond", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.io.other", "displayName": "IO other bytes/s", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.io.read", "displayName": "IO read bytes/s", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.io.write", "displayName": "IO write bytes/s", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.response.avg", "displayName": "Response time avg", "description": "", "unit": "Second", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.traffic.bytesReceived", "displayName": "Received bytes", "description": "", "unit": "Byte", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.traffic.bytesSent", "displayName": "Sent bytes", "description": "", "unit": "Byte", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.appService.traffic.requests", "displayName": "Requests count", "description": "", "unit": "Count", "entityType": ["AZURE_WEB_APP"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_web_app", "name": "Azure Web Application", "displayName": "Azure Web App", "index": 0, "type": "ENTITY"}, {"key": "instance", "name": "instance", "displayName": "instance", "index": 1, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.availableStorage", "displayName": "Available Storage", "description": "", "unit": "Byte", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.dataUsage", "displayName": "Data Usage", "description": "", "unit": "Byte", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.documentCount", "displayName": "Document Count", "description": "", "unit": "Count", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.documentQuota", "displayName": "Document Quota", "description": "", "unit": "Byte", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.indexUsage", "displayName": "Index Usage", "description": "", "unit": "Byte", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.metadataRequests", "displayName": "Metadata Requests", "description": "", "unit": "Count", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.normalizedRUConsumption", "displayName": "Normalized request units consumption", "description": "", "unit": "Percent", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.provisionedThroughput", "displayName": "Provisioned Throughput", "description": "", "unit": "Count", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 1, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.replicationLatency", "displayName": "Replication Latency", "description": "", "unit": "MilliSecond", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "Source Region", "name": "Source Region", "displayName": "Source Region", "index": 1, "type": "STRING"}, {"key": "Target Region", "name": "Target Region", "displayName": "Target Region", "index": 2, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.requestUnits", "displayName": "Total number of request units", "description": "", "unit": "Count", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.requests", "displayName": "Total number of requests", "description": "", "unit": "Count", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}, {"key": "Account Name", "name": "Account Name", "displayName": "Account Name", "index": 2, "type": "STRING"}, {"key": "DB Name", "name": "DB Name", "displayName": "DB Name", "index": 3, "type": "STRING"}, {"key": "Collection Name", "name": "Collection Name", "displayName": "Collection Name", "index": 4, "type": "STRING"}], "tags": []}, {"metricId": "builtin:cloud.azure.cosmos.serviceAvailability", "displayName": "Service Availability", "description": "", "unit": "Percent", "entityType": ["AZURE_COSMOS_DB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_cosmos_db", "name": "Azure Cosmos DB", "displayName": "Azure Cosmos DB", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.capture.backlog", "displayName": "Capture backlog", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.capture.bytes", "displayName": "Captured bytes", "description": "", "unit": "Byte", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.capture.msg", "displayName": "Captured messages", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.errors.quotaExceeded", "displayName": "<PERSON><PERSON><PERSON> exceeded errors", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.errors.server", "displayName": "Server errors", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.errors.user", "displayName": "User errors", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.requests.incoming", "displayName": "Incoming requests", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.requests.successful", "displayName": "Successful requests", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.requests.throttled", "displayName": "Throttled requests", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.traffic.bytesIn", "displayName": "Incoming bytes", "description": "", "unit": "BytePerMinute", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.traffic.bytesOut", "displayName": "Outgoing bytes", "description": "", "unit": "Byte", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.traffic.msgIn", "displayName": "Incoming messages", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHub.traffic.msgOut", "displayName": "Outgoing messages", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub", "name": "Azure Event Hub", "displayName": "Azure Event Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHubNamespace.connections.active", "displayName": "Active connections", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub_namespace", "name": "Azure Event Hub Namespace", "displayName": "Azure Event Hub Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHubNamespace.connections.closed", "displayName": "Closed connections", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub_namespace", "name": "Azure Event Hub Namespace", "displayName": "Azure Event Hub Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.eventHubNamespace.connections.opened", "displayName": "Opened connections", "description": "", "unit": "Count", "entityType": ["AZURE_EVENT_HUB_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_event_hub_namespace", "name": "Azure Event Hub Namespace", "displayName": "Azure Event Hub Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.command.abandoned", "displayName": "Commands abandoned", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.command.completed", "displayName": "Commands completed", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.command.rejected", "displayName": "Commands rejected", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.device.connected", "displayName": "Connected devices", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.device.dailyThroughputThrottling", "displayName": "Number of throttling errors", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.device.dataUsage", "displayName": "Total device data usage", "description": "", "unit": "Byte", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.device.registered", "displayName": "Total devices", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.eventHub.builtInEventHub.messages.delivered", "displayName": "Messages delivered to the built-in endpoint (messages/events)", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.eventHub.builtInEventHub.averageLatencyMs", "displayName": "Message latency for the built-in endpoint (messages/events)", "description": "", "unit": "MilliSecond", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.eventHub.messages.delivered", "displayName": "Messages delivered to Event Hub endpoints", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.eventHub.averageLatencyMs", "displayName": "Message latency for event hub endpoints", "description": "", "unit": "MilliSecond", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.messages.dropped", "displayName": "Dropped messages", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.messages.invalidForAllEndpoints", "displayName": "Invalid messages", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.messages.orphaned", "displayName": "Orphaned messages", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.messages.sendAttempts", "displayName": "Telemetry message send attempts", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.messages.sent", "displayName": "Telemetry messages sent", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.messages.sentToFallback", "displayName": "Messages matching fallback condition", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.serviceBus.queues.averageLatencyMs", "displayName": "Message latency for service bus queue endpoints", "description": "", "unit": "MilliSecond", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.serviceBus.queues.messagesDelivered", "displayName": "Messages delivered to service bus queue endpoints", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.serviceBus.topics.averageLatencyMs", "displayName": "Message latency for service bus topic endpoints", "description": "", "unit": "MilliSecond", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.serviceBus.topics.messagesDelivered", "displayName": "Messages delivered to service bus topic endpoints", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.storageEndpoints.avgLatencyMs", "displayName": "Message latency for storage endpoints", "description": "", "unit": "MilliSecond", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.storageEndpoints.blobsWritten", "displayName": "Blobs written to storage", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.storageEndpoints.bytesWritten", "displayName": "Data written to storage", "description": "", "unit": "Byte", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.iotHub.storageEndpoints.messageDelivered", "displayName": "Messages delivered to storage endpoints", "description": "", "unit": "Count", "entityType": ["AZURE_IOT_HUB"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_iot_hub", "name": "Azure IoT Hub", "displayName": "Azure Iot Hub", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.availability.dipTcp", "displayName": "Load balancer DIP TCP availability", "description": "", "unit": "Percent", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.availability.dipUdp", "displayName": "Load balancer DIP UDP availability", "description": "", "unit": "Percent", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.availability.vip", "displayName": "Load Balancer VIP availability", "description": "", "unit": "Percent", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.snatConnection.est", "displayName": "SNAT connections successful", "description": "", "unit": "Count", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.snatConnection.pending", "displayName": "SNAT connections pending", "description": "", "unit": "Count", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.snatConnection.rej", "displayName": "SNAT connections failed", "description": "", "unit": "Count", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.traffic.byteIn", "displayName": "Bytes received", "description": "", "unit": "Byte", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.traffic.byteOut", "displayName": "Bytes sent", "description": "", "unit": "Byte", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.traffic.packetIn", "displayName": "Packets received", "description": "", "unit": "Count", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.traffic.packetOut", "displayName": "Packets sent", "description": "", "unit": "Count", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.traffic.packetSynIn", "displayName": "SYN packets received", "description": "", "unit": "Count", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.loadbalancer.traffic.packetSynOut", "displayName": "SYN packets sent", "description": "", "unit": "Count", "entityType": ["AZURE_LOAD_BALANCER"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_load_balancer", "name": "Azure Load Balancer", "displayName": "Azure Load Balancer", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_vm", "name": "Azure Virtual Machine", "displayName": "Azure VM", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.cache.hits", "displayName": "Cache hits", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.cache.misses", "displayName": "<PERSON><PERSON> misses", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.cache.read", "displayName": "Read bytes/s", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.cache.write", "displayName": "Write bytes/s", "description": "", "unit": "BytePerSecond", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.commands.get", "displayName": "Get commands", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.commands.set", "displayName": "Set commands", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.commands.total", "displayName": "Total no. of processed commands", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.keys.evicted", "displayName": "No. of evicted keys", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.keys.expired", "displayName": "No. of expired keys", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.keys.total", "displayName": "Total no. of keys", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.memory.used", "displayName": "Used memory", "description": "", "unit": "Byte", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.memory.usedRss", "displayName": "Used memory RSS", "description": "", "unit": "Byte", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.connected", "displayName": "Connected clients", "description": "", "unit": "Count", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.load", "displayName": "Server load", "description": "", "unit": "Percent", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.redis.processorTime", "displayName": "Processor time", "description": "", "unit": "Percent", "entityType": ["AZURE_REDIS_CACHE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_redis_cache", "name": "Azure Cache for Redis", "displayName": "Azure Redis <PERSON>ache", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.region.vms.initializing", "displayName": "Number of starting VMs in region", "description": "", "unit": "Count", "entityType": ["AZURE_SUBSCRIPTION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_subscription", "name": "Azure Subscription", "displayName": "Azure subscription", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.region.vms.running", "displayName": "Number of active VMs in region", "description": "", "unit": "Count", "entityType": ["AZURE_SUBSCRIPTION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_subscription", "name": "Azure Subscription", "displayName": "Azure subscription", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.region.vms.stopped", "displayName": "Number of stopped VMs in region", "description": "", "unit": "Count", "entityType": ["AZURE_SUBSCRIPTION"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_subscription", "name": "Azure Subscription", "displayName": "Azure subscription", "index": 0, "type": "ENTITY"}, {"key": "dt.entity.azure_region", "name": "Azure region", "displayName": "Azure Region", "index": 1, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.connections.active", "displayName": "Total active connections", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.errors.server", "displayName": "Server errors", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.errors.user", "displayName": "User errors", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.messages.count", "displayName": "Count of messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.messages.countActive", "displayName": "Count of active messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.messages.countDeadLettered", "displayName": "Count of dead-lettered messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.messages.countScheduled", "displayName": "Count of scheduled messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.messages.incoming", "displayName": "Incoming messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.messages.outgoing", "displayName": "Outgoing messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.requests.incoming", "displayName": "Incoming requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.requests.successful", "displayName": "Total successful requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.requests.throttled", "displayName": "Throttled requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.cpu", "displayName": "Service bus premium namespace CPU usage metric", "description": "", "unit": "Percent", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.memory", "displayName": "Service bus premium namespace memory usage metric", "description": "", "unit": "Percent", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.namespace.size", "displayName": "Service bus size", "description": "", "unit": "Byte", "entityType": ["AZURE_SERVICE_BUS_NAMESPACE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_namespace", "name": "Azure Service Bus Namespace", "displayName": "Azure Service Bus Namespace", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.errors.server", "displayName": "Server errors", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.errors.user", "displayName": "User errors", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.messages.count", "displayName": "Count of messages in queue", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.messages.countActive", "displayName": "Count of active messages in a queue", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.messages.countDeadLettered", "displayName": "Count of dead-lettered messages in a queue", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.messages.countScheduled", "displayName": "Count of scheduled messages in a queue", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.messages.incoming", "displayName": "Incoming messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.messages.outgoing", "displayName": "Outgoing messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.requests.incoming", "displayName": "Incoming requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.requests.successful", "displayName": "Total successful requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.requests.throttled", "displayName": "Throttled requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.queue.size", "displayName": "Size of an queue", "description": "", "unit": "Byte", "entityType": ["AZURE_SERVICE_BUS_QUEUE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_queue", "name": "Azure Service Bus Queue", "displayName": "Azure Service Bus Queue", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.errors.server", "displayName": "Server errors", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.errors.user", "displayName": "User errors", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.messages.count", "displayName": "Count of messages in topic", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.messages.countActive", "displayName": "Count of active messages in a topic", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.messages.countDeadLettered", "displayName": "Count of dead-lettered messages in a topic", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.messages.countScheduled", "displayName": "Count of scheduled messages in a topic", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.messages.incoming", "displayName": "Incoming messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.messages.outgoing", "displayName": "Outgoing messages", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.requests.incoming", "displayName": "Incoming requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.requests.successful", "displayName": "Total successful requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.requests.throttled", "displayName": "Throttled requests", "description": "", "unit": "Count", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.serviceBus.topic.size", "displayName": "Size of a topic", "description": "", "unit": "Byte", "entityType": ["AZURE_SERVICE_BUS_TOPIC"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_service_bus_topic", "name": "Azure Service Bus Topic", "displayName": "Azure Service Bus Topic", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.connections.blockedByFirewall", "displayName": "Blocked by firewall", "description": "", "unit": "Count", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.connections.failed", "displayName": "Failed connections", "description": "", "unit": "Count", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.connections.successful", "displayName": "Successful connections", "description": "", "unit": "Count", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.dtu.limit.count", "displayName": "DTU limit", "description": "", "unit": "Count", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.dtu.limit.used", "displayName": "DTU used", "description": "", "unit": "Count", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.dtu.consumptionPerc", "displayName": "DTU percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.io.dataRead", "displayName": "Data I/O percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.io.logWrite", "displayName": "Log I/O percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.storage.percent", "displayName": "Database size percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.storage.totalBytes", "displayName": "Total database size", "description": "", "unit": "Byte", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.storage.xtpPercent", "displayName": "In-Memory OLTP storage percent", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.cpuPercent", "displayName": "CPU percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.deadlocks", "displayName": "Deadlocks", "description": "", "unit": "Count", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "value"], "defaultAggregation": {"type": "value"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.sessions", "displayName": "Sessions percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlDatabase.workers", "displayName": "Workers percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_DATABASE"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_database", "name": "Azure SQL Database", "displayName": "Azure SQL Database", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlElasticPool.dtu.storage.limitBytes", "displayName": "Storage limit", "description": "", "unit": "Byte", "entityType": ["AZURE_SQL_ELASTIC_POOL"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_elastic_pool", "name": "Azure SQL Elastic Pool", "displayName": "Azure SQL Elastic Pool", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlElasticPool.dtu.storage.percent", "displayName": "Database size percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_ELASTIC_POOL"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_elastic_pool", "name": "Azure SQL Elastic Pool", "displayName": "Azure SQL Elastic Pool", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlElasticPool.dtu.storage.usedBytes", "displayName": "Storage used", "description": "", "unit": "Byte", "entityType": ["AZURE_SQL_ELASTIC_POOL"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_elastic_pool", "name": "Azure SQL Elastic Pool", "displayName": "Azure SQL Elastic Pool", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlElasticPool.dtu.storage.xtpPercent", "displayName": "In-memory OLTP storage percent", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_ELASTIC_POOL"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_elastic_pool", "name": "Azure SQL Elastic Pool", "displayName": "Azure SQL Elastic Pool", "index": 0, "type": "ENTITY"}], "tags": []}, {"metricId": "builtin:cloud.azure.sqlElasticPool.dtu.consumption", "displayName": "DTU percentage", "description": "", "unit": "Percent", "entityType": ["AZURE_SQL_ELASTIC_POOL"], "aggregationTypes": ["auto", "avg", "max", "min"], "defaultAggregation": {"type": "avg"}, "dimensionDefinitions": [{"key": "dt.entity.azure_sql_elastic_pool", "name": "Azure SQL Elastic Pool", "displayName": "Azure SQL Elastic Pool", "index": 0, "type": "ENTITY"}], "tags": []}]}